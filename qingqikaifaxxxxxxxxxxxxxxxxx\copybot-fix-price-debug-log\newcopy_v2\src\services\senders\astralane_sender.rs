use anyhow::{anyhow, Result};
use async_trait::async_trait;
use reqwest::{header::CONTENT_TYPE, Client};
use serde::Deserialize;
use serde_json::json;
use solana_sdk::transaction::Transaction;
use std::cell::RefCell;
use base64::engine::general_purpose::STANDARD as B64_ENGINE;
use base64::Engine;
use tracing::{error, info, warn};
use rand::seq::SliceRandom;

use crate::config::AcceleratorConfig;
use super::{TransactionSenderBackend, rpc_sender::RpcSender};

// --- Astralane 硬编码的小费地址 ---
// 基于官方文档说明，Astralane 最低小费 10,000 lamports (0.00001 SOL)
const ASTRALANE_TIP_ADDRESSES: [&str; 4] = [
    "astrazznxsGUhWShqgNtAdfrzP2G83DzcWVJDxwV9bF",
    "astra4uejePWneqNaJKuFFA8oonqCE1sqF6b45kDMZm",
    "astra9xWY93QyfG6yM8zwsKsRodscjQ2uU2HKNL5prk",
    "astraRVUuTHjpwEVvNBeQEgwYx9w9CFyfxjYoobCZhL",
];

thread_local! {
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct AstralaneResponse {
    result: String,
}

pub struct AstralaneSender {
    client: Client,
    config: AcceleratorConfig,
    fallback_sender: RpcSender,
    skip_preflight: bool,
}

impl AstralaneSender {
    pub fn new(client: Client, config: AcceleratorConfig, fallback_sender: RpcSender, skip_preflight: bool) -> Self {
        Self {
            client,
            config,
            fallback_sender,
            skip_preflight,
        }
    }

    pub fn get_random_tip_account() -> &'static str {
        let mut rng = rand::thread_rng();
        ASTRALANE_TIP_ADDRESSES.choose(&mut rng).unwrap()
    }
}

#[async_trait]
impl TransactionSenderBackend for AstralaneSender {
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        // 从配置中获取激活的提供商URL和Key
        let (api_url, api_key) = match self.config.get_active_provider_config() {
            Some(config) => config,
            None => {
                // 如果配置无效或未启用，直接回退
                warn!("Astralane 加速器未启用或提供商配置无效。将回退到标准RPC。");
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let tx_base64 = SER_BUF.with(|ser_cell| {
            B64_BUF.with(|b64_cell| {
                let mut ser_buf = ser_cell.borrow_mut();
                ser_buf.clear();
                bincode::serialize_into(&mut *ser_buf, tx)
                    .map_err(|e| anyhow!("bincode 序列化失败: {e}"))?;
                let mut b64_buf = b64_cell.borrow_mut();
                b64_buf.clear();
                B64_ENGINE.encode_string(ser_buf.as_slice(), &mut *b64_buf);
                Ok::<_, anyhow::Error>(b64_buf.clone())
            })
        })?;

        // 构造标准 Solana RPC 请求体
        let body = json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendTransaction",
            "params": [
                tx_base64,
                {
                    "encoding": "base64",
                    "skipPreflight": self.skip_preflight,
                    "preflightCommitment": "confirmed",
                    "maxRetries": 3
                }
            ]
        });

        // 构造请求URL，API key 已经包含在 URL 中
        let url_with_key = if api_url.contains("?api-key=") {
            api_url.replace("?api-key=", &format!("?api-key={}", api_key))
        } else {
            format!("{}?api-key={}", api_url.trim_end_matches('/'), api_key)
        };

        let resp = self.client
            .post(&url_with_key)
            .header(CONTENT_TYPE, "application/json")
            .json(&body)
            .send()
            .await;
        
        let resp = match resp {
            Ok(r) => r,
            Err(e) => {
                error!("发送到Astralane时发生网络错误: {}。将回退到标准RPC。", e);
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let status = resp.status();
        let text = resp.text().await?;

        if !status.is_success() {
            warn!("Astralane 加速器HTTP状态非成功: {} – {}。将回退到标准RPC。", status, text);
            return self.fallback_sender.send_transaction(tx).await;
        }

        // 尝试解析标准 JSON-RPC 响应格式
        match serde_json::from_str::<serde_json::Value>(&text) {
            Ok(v) => {
                // 标准 JSON-RPC 格式 {"result": "<signature>"}
                if let Some(sig) = v.get("result").and_then(|r| r.as_str()) {
                    info!("⚡️ 交易已通过 Astralane 加速器提交: {}", sig);
                    return Ok(sig.to_string());
                }
                
                error!("Astralane 返回成功但缺少 result 字段，原文: {}。回退RPC。", text);
                self.fallback_sender.send_transaction(tx).await
            }
            Err(e) => {
                error!("解析 Astralane JSON 失败: {}，原文: {}。回退RPC。", e, text);
                self.fallback_sender.send_transaction(tx).await
            }
        }
    }
}