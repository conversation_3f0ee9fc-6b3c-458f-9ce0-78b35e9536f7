use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

/// Bonk协议专用常量
pub const BONK_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj");

/// Bonk协议交易指令类型
#[allow(dead_code)]  // 暂时未使用，后续扩展时会用到
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BonkInstructionType {
    Buy,
    Sell,
    Unknown,
}

/// Bonk协议特有的交易数据
#[allow(dead_code)]  // 暂时未使用，后续扩展时会用到
#[derive(Debug, Clone)]
pub struct BonkTradeData {
    pub mint: Pubkey,
    pub pool_state: Pubkey,
    pub signer: Pubkey,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
} 