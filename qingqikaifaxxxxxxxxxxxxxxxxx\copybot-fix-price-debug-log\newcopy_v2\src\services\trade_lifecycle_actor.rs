use crate::shared::types::HotPathTrade;
use crate::services::sell_executor_trait::SellExecutorTrait;
use crate::services::auto_suspend_manager::AutoSuspendManager;
use crate::shared::types::WalletConfig;
use crate::strategies::volatility_breakout::VolatilityBreakoutDetector;
use solana_sdk::pubkey::Pubkey;
use std::pin::Pin;
use std::time::{Duration, Instant};
use tokio::sync::broadcast;
use tokio::time::{self, Sleep};
use tracing::{info, warn};
use std::sync::Arc;
use dashmap::DashMap;

// --- 状态定义 ---

/// 定义了交易生命周期中的不同阶段。
#[derive(Debug)]
enum LifecycleState {
    /// 活跃期：Actor正在积极监控价格并管理计时器。
    Active(Pin<Box<Sleep>>),
    /// 结束状态：交易已卖出或终止。
    Done,
}

// --- Actor 定义 ---

/// 为每笔交易独立创建的Actor，负责管理其完整的生命周期。
pub struct TradeLifecycleActor {
    // 交易元数据
    trade_id: String,
    mint: Pubkey,
    buy_price: f64,
    
    // 状态与数据
    state: LifecycleState,
    deadline: Instant, // 核心变量：交易的最终到期时间点
    remaining_amount: u64,
    initial_amount: u64,
    highest_price_since_buy: f64, // 从 highest_price_seen 重命名，含义更清晰
    current_profit_stage: u32,    // 从 take_profit_level 重命名，用于多种策略
    
    // 依赖的服务
    sell_executor: Arc<dyn SellExecutorTrait + Send + Sync>,
    price_receiver: broadcast::Receiver<(Pubkey, f64)>,
    revoked_mints: Arc<DashMap<Pubkey, u64>>,
    
    // 内部状态
    known_revocations: u64,
    // 跟卖状态：已执行的累计卖出比例
    follow_sell_executed_percentage: f64,
    
    // 配置
    wallet_config: WalletConfig,
    trade_info: HotPathTrade,

    // 自动暂停管理器
    auto_suspend_manager: Option<Arc<AutoSuspendManager>>,
    
    // 波动率突破检测器（仅当策略为volatility时使用）
    volatility_detector: Option<VolatilityBreakoutDetector>,
}

impl TradeLifecycleActor {
    /// 创建一个新的 TradeLifecycleActor 实例。
    pub fn new(
        trade_info: HotPathTrade,
        wallet_config: WalletConfig,
        actual_buy_amount: u64,
        sell_executor: Arc<dyn SellExecutorTrait + Send + Sync>,
        price_receiver: broadcast::Receiver<(Pubkey, f64)>,
        revoked_mints: Arc<DashMap<Pubkey, u64>>,
        known_revocations: u64,
        auto_suspend_manager: Option<Arc<AutoSuspendManager>>,
    ) -> Self {
        let now = Instant::now();
        let initial_hold_duration = Duration::from_millis(wallet_config.entry_confirmation_ms.unwrap_or(10000));
        
        let deadline = now + initial_hold_duration;

        let mint = trade_info.mint_pubkey;
        let trade_id = trade_info.trade_id.clone();
        let buy_price = trade_info.price;
        
        // 只有当策略选择为volatility时才初始化波动率检测器
        let volatility_detector = if wallet_config.take_profit_strategy.as_deref() == Some("volatility") {
            // 使用配置的参数，如果没有则使用默认值
            Some(VolatilityBreakoutDetector::new(
                wallet_config.volatility_bb_window_size.unwrap_or(1000),
                wallet_config.volatility_bb_stddev.unwrap_or(1.8),
                wallet_config.volatility_atr_samples.unwrap_or(100),
                wallet_config.volatility_atr_multiplier.unwrap_or(1.3),
                wallet_config.volatility_sell_percent.unwrap_or(40.0),
                wallet_config.volatility_cooldown_ms.unwrap_or(500),
            ))
        } else {
            None
        };
        
        info!(
            "[Trade: {}] 创建新的生命周期 Actor，买入价: {}, 数量: {}, 初始持仓 {} 毫秒",
            trade_id,
            buy_price,
            actual_buy_amount,
            initial_hold_duration.as_millis()
        );

        Self {
            trade_id,
            mint,
            buy_price,
            state: LifecycleState::Active(Box::pin(time::sleep(initial_hold_duration))),
            deadline,
            remaining_amount: actual_buy_amount,
            initial_amount: actual_buy_amount,
            highest_price_since_buy: buy_price,
            current_profit_stage: 1, // 阶段从1开始计数
            sell_executor,
            price_receiver,
            revoked_mints,
            known_revocations,
            follow_sell_executed_percentage: 0.0,
            wallet_config,
            trade_info,
            auto_suspend_manager,
            volatility_detector,
        }
    }

    /// 启动 Actor 的主事件循环。
    pub async fn start(mut self) {
        info!("[Trade: {}] Actor 已启动，开始监控... (已知撤销次数: {})", self.trade_id, self.known_revocations);
        loop {
            // 在每次循环开始时，检查自己的mint是否已被撤销或收到跟卖信号
            let signal_value = self.revoked_mints.get(&self.mint).map(|v| *v.value()).unwrap_or(0);
            
            // 🚀 检查是否为跟卖策略信号 (>= 1000000)
            if signal_value >= 1000000 {
                let target_total_sell_percentage = (signal_value - 1000000) as f64 / 100.0;
                let current_price = self.highest_price_since_buy; // 使用当前已知的最新价格
                
                // 计算还需要卖出的比例（基于初始持仓）
                let additional_sell_percentage = target_total_sell_percentage - self.follow_sell_executed_percentage;
                
                if additional_sell_percentage > 0.1 { // 只有当需要额外卖出超过0.1%时才执行
                    info!("[Trade: {}] [跟卖执行] 目标{:.1}% 已执行{:.1}% 需要{:.1}% @{}", 
                          self.trade_id, target_total_sell_percentage, self.follow_sell_executed_percentage, additional_sell_percentage, current_price);
                    
                    // 计算基于初始持仓的卖出数量
                    let sell_amount = (self.initial_amount as f64 * additional_sell_percentage / 100.0) as u64;
                    let sell_amount = sell_amount.min(self.remaining_amount); // 不能超过剩余持仓
                    
                    if sell_amount > 0 {
                        let full_reason = format!("跟卖至{:.1}%", target_total_sell_percentage);
                        
                        // 直接调用卖出执行器而不是execute_partial_sell，因为我们要精确控制数量
                        info!("[Trade: {}] 执行跟卖，数量: {}, 原因: {}", self.trade_id, sell_amount, full_reason);
                        
                        if let Err(e) = self.sell_executor.execute_sell(
                            self.trade_info.clone(),
                            &self.wallet_config,
                            sell_amount,
                            Duration::from_secs(0),
                            &full_reason,
                            current_price,
                            false, // 部分卖出，不要关闭ATA
                        ).await {
                            warn!("[Trade: {}] [跟卖失败] {}", self.trade_id, e);
                        } else {
                            self.remaining_amount = self.remaining_amount.saturating_sub(sell_amount);
                            let actual_sell_percentage = (sell_amount as f64 / self.initial_amount as f64) * 100.0;
                            self.follow_sell_executed_percentage += actual_sell_percentage;
                            
                            info!("[Trade: {}] [跟卖成功] 剩余{} 累计已卖{:.1}%", 
                                  self.trade_id, self.remaining_amount, self.follow_sell_executed_percentage);
                            
                            if self.remaining_amount == 0 {
                                info!("[Trade: {}] 仓位已全部卖出。", self.trade_id);
                                self.state = LifecycleState::Done;
                                
                                // 完全卖出时记录交易完成
                                if let Some(auto_suspend_manager) = &self.auto_suspend_manager {
                                    let profit_pct = (current_price - self.buy_price) / self.buy_price * 100.0;
                                    auto_suspend_manager.record_trade_completion(
                                        &self.trade_info.signer,  // 跟随的钱包地址
                                        profit_pct,
                                        &self.wallet_config,
                                    ).await;
                                }
                            }
                        }
                    }
                }
                
                // 重置信号，避免重复触发
                self.revoked_mints.insert(self.mint, self.known_revocations);
                
            } else if signal_value > self.known_revocations {
                // 正常的撤销信号处理
                info!("[Trade: {}] 检测到新的外部平仓事件 (当前: {}, 已知: {})，策略终止。", 
                    self.trade_id, 
                    signal_value, 
                    self.known_revocations
                );
                self.state = LifecycleState::Done;
                break;
            }

            let timer = match &mut self.state {
                LifecycleState::Active(timer) => timer,
                LifecycleState::Done => {
                    info!("[Trade: {}] Actor 完成任务，正在终止。", self.trade_id);
                    break;
                }
            };

            tokio::select! {
                // 等待计时器到期
                _ = timer => {
                    self.handle_timer_expiry().await;
                },
                // 等待价格更新
                res = self.price_receiver.recv() => {
                    match res {
                        Ok((mint, price)) => {
                            if mint == self.mint {
                                self.handle_price_update(price).await;
                            }
                        }
                        Err(broadcast::error::RecvError::Lagged(n)) => {
                            warn!("[Trade: {}] 价格通道滞后了 {}条消息", self.trade_id, n);
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            warn!("[Trade: {}] 价格通道已关闭，Actor 正在终止。", self.trade_id);
                            self.state = LifecycleState::Done;
                        }
                    }
                }
            }
        }
    }

    /// 处理计时器到期事件。
    async fn handle_timer_expiry(&mut self) {
        // 立即检查状态，防止竞争条件
        if matches!(self.state, LifecycleState::Done) || self.remaining_amount == 0 {
            return;
        }
        
        info!("[Trade: {}] 持仓时间到期，执行清仓。", self.trade_id);
        let current_price = self.highest_price_since_buy; // 用最近的最高价作为清算参考价
        let _ = self.liquidate_position("持仓时间到期", Some(current_price)).await;
    }

    /// 处理价格更新事件。
    async fn handle_price_update(&mut self, price: f64) {
        // 优先检查状态，防止与清仓操作冲突
        if matches!(self.state, LifecycleState::Done) || self.remaining_amount == 0 {
            return;
        }

        let mut action_taken = false;
        
        // --- 1. 更新最高价 ---
        if price > self.highest_price_since_buy {
            self.highest_price_since_buy = price;
        }
        
        // --- 2. 检查硬性止损条件 ---
        if let Some(hard_stop_pct) = self.wallet_config.hard_stop_loss_pct {
            if price <= self.buy_price * (1.0 - hard_stop_pct / 100.0) {
                let reason = format!("硬止损触发于价格 {}", price);
                let _ = self.liquidate_position(&reason, Some(price)).await;
                action_taken = true;
            }
        }
        
        // --- 3. 检查回调止损条件 (仅在未触发硬止损时) ---
        if !action_taken {
            if let Some(callback_stop_pct) = self.wallet_config.callback_stop_pct {
                if price <= self.highest_price_since_buy * (1.0 - callback_stop_pct / 100.0) {
                    let reason = format!("回调止损触发于价格 {} (最高价 {})", price, self.highest_price_since_buy);
                    let _ = self.liquidate_position(&reason, Some(price)).await;
                    action_taken = true;
                }
            }
        }

        // --- 4. 根据所选策略执行止盈逻辑 (仅在未触发任何止损时) ---
        if !action_taken {
            let strategy = self.wallet_config.take_profit_strategy.as_deref().unwrap_or("standard");

            match strategy {
                "trailing" => {
                    if let Some(trailing_pct) = self.wallet_config.trailing_stop_profit_percentage {
                        if self.highest_price_since_buy > self.buy_price {
                            let trigger_price = self.highest_price_since_buy * (1.0 - trailing_pct / 100.0);
                            if price <= trigger_price {
                                let reason = format!(
                                    "追踪止盈触发于价格 {} (从最高价 {} 回撤 > {}%)",
                                    price, self.highest_price_since_buy, trailing_pct
                                );
                                let _ = self.liquidate_position(&reason, Some(price)).await;
                                action_taken = true;
                            }
                        }
                    }
                }
                "exponential" => {
                    if let (Some(trigger_step_pct), Some(base_portion_pct), Some(power)) = (
                        self.wallet_config.exponential_sell_trigger_step_pct,
                        self.wallet_config.exponential_sell_base_portion_pct,
                        self.wallet_config.exponential_sell_power,
                    ) {
                        let profit_target_pct = trigger_step_pct * self.current_profit_stage as f64;
                        let take_profit_price = self.buy_price * (1.0 + profit_target_pct / 100.0);

                        if price >= take_profit_price {
                            let sell_portion_pct = base_portion_pct * (self.current_profit_stage as f64).powf(power);
                            let sell_portion_pct = sell_portion_pct.min(100.0);
                            
                            let reason = format!(
                                "指数加码卖出第 {} 阶段 (目标: +{:.2}%, 卖出: {:.2}%) 触发于价格 {}",
                                self.current_profit_stage, profit_target_pct, sell_portion_pct, price
                            );

                            if sell_portion_pct >= 100.0 {
                                let _ = self.liquidate_position(&reason, Some(price)).await;
                            } else {
                                self.execute_partial_sell(sell_portion_pct, &reason, price, true).await;
                                // 部分卖出成功后，延长计时器
                                let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
                                self.extend_deadline("指数加码卖出", profit_pct);
                            }
                            
                            self.current_profit_stage += 1;
                            action_taken = true;
                        }
                    }
                }
                "volatility" => {
                    if let Some(ref mut detector) = self.volatility_detector {
                        let timestamp_ms = crate::strategies::volatility_breakout::VolatilityBreakoutDetector::get_current_timestamp_ms();
                        if let Some((reason, sell_percent)) = detector.on_price_event(price, timestamp_ms) {
                            info!("[Trade: {}] 🔥 {}", self.trade_id, reason);
                            self.execute_partial_sell(sell_percent, &reason, price, true).await;
                            // 部分卖出成功后，只有在未完全清仓时才延长计时器
                            if !matches!(self.state, LifecycleState::Done) {
                                let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
                                self.extend_deadline("波动率突破", profit_pct);
                            }
                            action_taken = true;
                        }
                    }
                }
                "standard" | _ => {
                    if let (Some(start_pct), Some(step_pct), Some(sell_portion_pct)) = (
                        self.wallet_config.take_profit_start_pct,
                        self.wallet_config.take_profit_step_pct,
                        self.wallet_config.take_profit_sell_portion_pct,
                    ) {
                        let next_tp_level = (self.current_profit_stage - 1) as f64;
                        let target_profit_pct = start_pct + (next_tp_level * step_pct);
                        let take_profit_price = self.buy_price * (1.0 + target_profit_pct / 100.0);

                        if price >= take_profit_price {
                            let reason = format!("标准止盈第 {} 阶段触发于价格 {}", self.current_profit_stage, price);
                            self.execute_partial_sell(sell_portion_pct, &reason, price, true).await;
                            self.current_profit_stage += 1;
                            // 部分卖出成功后，延长计时器
                            let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
                            self.extend_deadline("分步止盈", profit_pct);
                            action_taken = true;
                        }
                    }
                }
            }
        }

        // --- 5. 检查是否满足累加持仓时间条件 (仅在未触发任何卖出操作时) ---
        if !action_taken {
            if let Some(trigger_pct) = self.wallet_config.dynamic_hold_trigger_pct {
                let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
                if profit_pct.abs() >= trigger_pct {
                    let reason = if profit_pct > 0.0 {
                        "涨幅满足条件"
                    } else {
                        "跌幅满足条件"
                    };
                    self.extend_deadline(reason, profit_pct);
                }
            }
        }
    }

    /// 核心累加逻辑：延长截止时间并重置计时器
    fn extend_deadline(&mut self, reason: &str, current_profit_pct: f64) {
        // 防止在清仓状态下重新激活Timer
        if matches!(self.state, LifecycleState::Done) {
            return;
        }
        
        let now = Instant::now();
        let extension_duration = Duration::from_millis(self.wallet_config.dynamic_hold_extend_ms.unwrap_or(5000));
        let max_countdown_duration = Duration::from_millis(self.wallet_config.dynamic_hold_max_ms.unwrap_or(300000)); // 硬编码一个默认值，防止配置缺失

        // 1. 计算当前倒计时还剩多少时间
        let remaining_duration = self.deadline.saturating_duration_since(now);

        // 2. 累加延长时间
        let proposed_duration = remaining_duration + extension_duration;

        // 3. 将新的倒计时时长限制在最大值以内
        let new_countdown_duration = std::cmp::min(proposed_duration, max_countdown_duration);

        // 4. 根据新的、被限制过的倒计时时长，计算出未来的新截止时间点
        self.deadline = now + new_countdown_duration;
        
        // 5. 记录日志并重置计时器
        let reason_with_pct = format!("{} ({:+.2}%)", reason, current_profit_pct);
        info!(
            "[Trade: {}] {}, 倒计时延长至 {:.2}毫秒。",
            self.trade_id,
            reason_with_pct,
            new_countdown_duration.as_millis()
        );
        
        self.state = LifecycleState::Active(Box::pin(time::sleep(new_countdown_duration)));
    }
    
    /// 执行部分卖出（止盈）
    async fn execute_partial_sell(&mut self, sell_portion_pct: f64, reason: &str, current_price: f64, partial_threshold_on: bool) {
        // 防止在清仓状态下执行部分卖出
        if matches!(self.state, LifecycleState::Done) || self.remaining_amount == 0 { 
            return; 
        }

        // --- 最小卖出/持仓比例保护 ---
        let min_pct = if partial_threshold_on {
            self.wallet_config.min_partial_sell_pct.unwrap_or(0.0)
        } else { 0.0 };

        // 1. 若剩余仓位占初始仓位比例 ≤ 阈值 ⇒ 直接清仓
        if min_pct > 0.0 {
            let remain_ratio = self.remaining_amount as f64 * 100.0 / self.initial_amount as f64;
            if remain_ratio <= min_pct {
                let _ = self.liquidate_position("尾部仓位比例过小直接清仓", Some(current_price)).await;
                return;
            }
        }

        // 2. 计算本次卖出量
        let sell_amount = (self.remaining_amount as f64 * sell_portion_pct / 100.0) as u64;

        // 3. 正常部分卖出逻辑
        
        // 计算本次卖出的盈亏
        let profit_pct = (current_price - self.buy_price) / self.buy_price * 100.0;
        let pnl_info = format!("买入价: {:.9}, 卖出价: {:.9}, 盈亏: {:.2}%", self.buy_price, current_price, profit_pct);

        info!("[Trade: {}] 执行部分卖出，数量: {}, 原因: {}. {}", self.trade_id, sell_amount, reason, pnl_info);
        
        if let Err(e) = self.sell_executor.execute_sell(
            self.trade_info.clone(),
            &self.wallet_config,
            sell_amount,
            Duration::from_secs(0),
            reason,
            current_price,
            false, // 部分卖出，不要关闭ATA
        ).await {
            // 如果是跟卖，显示简化的失败日志
            if reason.contains("跟卖") {
                warn!("[Trade: {}] [跟卖失败] {}", self.trade_id, e);
            } else {
                warn!("[Trade: {}] 部分卖出失败: {}", self.trade_id, e);
            }
        } else {
            self.remaining_amount = self.remaining_amount.saturating_sub(sell_amount);
            // 如果是跟卖，显示简化的成功日志
            if reason.contains("跟卖") {
                info!("[Trade: {}] [跟卖成功] 剩余{}", self.trade_id, self.remaining_amount);
            } else {
                info!("[Trade: {}] 部分卖出交易已提交。剩余持仓: {}", self.trade_id, self.remaining_amount);
            }
            if self.remaining_amount == 0 {
                 info!("[Trade: {}] 仓位已全部卖出。", self.trade_id);
                self.state = LifecycleState::Done;

                // 完全卖出时记录交易完成
                if let Some(auto_suspend_manager) = &self.auto_suspend_manager {
                    let profit_pct = (current_price - self.buy_price) / self.buy_price * 100.0;
                    auto_suspend_manager.record_trade_completion(
                        &self.trade_info.signer,  // 跟随的钱包地址
                        profit_pct,
                        &self.wallet_config,
                    ).await;
                }
            }
        }
    }

    /// 执行清仓操作。
    async fn liquidate_position(&mut self, reason: &str, current_price: Option<f64>) -> Result<(), anyhow::Error> {
        if matches!(self.state, LifecycleState::Done) || self.remaining_amount == 0 {
            return Ok(());
        }

        // 保存要卖出的数量，然后立即设置状态，防止竞争条件
        let amount_to_sell = self.remaining_amount;
        self.state = LifecycleState::Done; // 先改变状态，防止重入
        self.remaining_amount = 0; // 立即设置为0，防止在异步卖出期间的竞争条件

        // 根据是否有当前价格，计算盈亏信息
        let pnl_info = if let Some(price) = current_price {
            let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
            format!("当前价格: {:.9}, 盈亏: {:.2}%", price, profit_pct)
        } else {
            "无实时价格信息".to_string()
        };

        info!("[Trade: {}] 执行清仓，数量: {}, 原因: {}. {}", self.trade_id, amount_to_sell, reason, pnl_info);

        // 清仓决策时间应该是瞬时的，与部分卖出保持一致
        let decision_dur = Duration::from_secs(0);

        let _ = self.sell_executor.execute_sell(
            self.trade_info.clone(),
            &self.wallet_config,
            amount_to_sell, // 使用保存的数量
            decision_dur,
            reason,
            current_price.unwrap_or(self.highest_price_since_buy),
            true, // 清仓，需要关闭ATA
        ).await;

        // remaining_amount 已经在上面设置为0了

        // 记录交易完成到自动暂停管理器
        if let (Some(auto_suspend_manager), Some(price)) = (&self.auto_suspend_manager, current_price) {
            let profit_pct = (price - self.buy_price) / self.buy_price * 100.0;
            auto_suspend_manager.record_trade_completion(
                &self.trade_info.signer,  // 跟随的钱包地址
                profit_pct,
                &self.wallet_config,
            ).await;
        }

        Ok(())
    }
} 