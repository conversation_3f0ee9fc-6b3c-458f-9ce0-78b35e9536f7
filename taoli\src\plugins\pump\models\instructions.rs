/// PumpFun指令定义和解析
/// 
/// 基于参考模块实现的PumpFun程序指令结构

use borsh::{BorshDeserialize, BorshSerialize};
use serde::{Deserialize, Serialize};
use std::io::{Read, ErrorKind};
use crate::plugins::pump::types::constants::*;
use log::debug;

/// PumpFun程序指令枚举
#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum PumpProgramIx {
    /// 初始化指令
    Initialize,
    /// 设置参数指令
    SetParams(SetParamsIxArgs),
    /// 创建代币指令
    Create(CreateIxArgs),
    /// 购买指令
    Buy(BuyIxArgs),
    /// 出售指令
    Sell(SellIxArgs),
    /// 提取指令
    Withdraw,
}

impl PumpProgramIx {
    /// 获取指令名称
    pub fn name(&self) -> String {
        match self {
            Self::Initialize => "initialize".to_string(),
            Self::SetParams(_) => "setParams".to_string(),
            Self::Create(_) => "create".to_string(),
            Self::Buy(_) => "buy".to_string(),
            Self::Sell(_) => "sell".to_string(),
            Self::Withdraw => "withdraw".to_string(),
        }
    }

    /// 从字节数据反序列化指令
    pub fn deserialize(buf: &[u8]) -> std::io::Result<Self> {
        // 检查数据长度
        if buf.len() < 8 {
            return Err(std::io::Error::new(
                ErrorKind::InvalidInput,
                format!("指令数据长度不足，需要至少8字节，实际: {}", buf.len()),
            ));
        }

        let mut reader = buf;
        let mut maybe_discm = [0u8; 8];
        reader.read_exact(&mut maybe_discm)?;
        

        
        match maybe_discm {
            INITIALIZE_IX_DISCM => {
    
                Ok(Self::Initialize)
            },
            SET_PARAMS_IX_DISCM => {
    
                let args_result = <SetParamsIxArgs as BorshDeserialize>::deserialize(&mut reader);
                match args_result {
                    Ok(args) => Ok(Self::SetParams(args)),
                    Err(e) => Err(std::io::Error::new(
                        ErrorKind::InvalidData,
                        format!("SetParams参数解析失败: {}", e),
                    )),
                }
            },
            CREATE_IX_DISCM => {
    
                let args_result = <CreateIxArgs as BorshDeserialize>::deserialize(&mut reader);
                match args_result {
                    Ok(args) => Ok(Self::Create(args)),
                    Err(e) => Err(std::io::Error::new(
                        ErrorKind::InvalidData,
                        format!("Create参数解析失败: {}", e),
                    )),
                }
            },
            BUY_IX_DISCM => {
    
                let args_result = <BuyIxArgs as BorshDeserialize>::deserialize(&mut reader);
                match args_result {
                    Ok(args) => Ok(Self::Buy(args)),
                    Err(e) => Err(std::io::Error::new(
                        ErrorKind::InvalidData,
                        format!("Buy参数解析失败: {}", e),
                    )),
                }
            },
            SELL_IX_DISCM => {
    
                let args_result = <SellIxArgs as BorshDeserialize>::deserialize(&mut reader);
                match args_result {
                    Ok(args) => Ok(Self::Sell(args)),
                    Err(e) => Err(std::io::Error::new(
                        ErrorKind::InvalidData,
                        format!("Sell参数解析失败: {}", e),
                    )),
                }
            },
            WITHDRAW_IX_DISCM => {
    
                Ok(Self::Withdraw)
            },
            _ => {
                Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("未知指令判别符: {:?}", maybe_discm),
                ))
            }
        }
    }

    /// 序列化指令到字节数据
    pub fn serialize<W: std::io::Write>(&self, mut writer: W) -> std::io::Result<()> {
        match self {
            Self::Initialize => writer.write_all(&INITIALIZE_IX_DISCM),
            Self::SetParams(args) => {
                writer.write_all(&SET_PARAMS_IX_DISCM)?;
                BorshSerialize::serialize(&args, &mut writer)
            }
            Self::Create(args) => {
                writer.write_all(&CREATE_IX_DISCM)?;
                BorshSerialize::serialize(&args, &mut writer)
            }
            Self::Buy(args) => {
                writer.write_all(&BUY_IX_DISCM)?;
                BorshSerialize::serialize(&args, &mut writer)
            }
            Self::Sell(args) => {
                writer.write_all(&SELL_IX_DISCM)?;
                BorshSerialize::serialize(&args, &mut writer)
            }
            Self::Withdraw => writer.write_all(&WITHDRAW_IX_DISCM),
        }
    }

    /// 转换为字节向量
    pub fn try_to_vec(&self) -> std::io::Result<Vec<u8>> {
        let mut data = Vec::new();
        self.serialize(&mut data)?;
        Ok(data)
    }
}

/// 设置参数指令参数
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct SetParamsIxArgs {
    /// 费用基点
    pub fee_basis_points: Option<u16>,
    /// 初始虚拟代币储备
    pub initial_virtual_token_reserves: Option<u64>,
    /// 初始虚拟SOL储备
    pub initial_virtual_sol_reserves: Option<u64>,
    /// 初始真实代币储备
    pub initial_real_token_reserves: Option<u64>,
    /// 代币总供应量
    pub token_total_supply: Option<u64>,
}

/// 创建代币指令参数
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct CreateIxArgs {
    /// 代币名称
    pub name: String,
    /// 代币符号
    pub symbol: String,
    /// 代币URI
    pub uri: String,
}

/// 购买指令参数
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct BuyIxArgs {
    /// 购买数量
    pub amount: u64,
    /// 最大SOL成本
    pub max_sol_cost: u64,
}

/// 出售指令参数
#[derive(BorshDeserialize, BorshSerialize, Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct SellIxArgs {
    /// 出售数量
    pub amount: u64,
    /// 最小SOL输出
    pub min_sol_output: u64,
}
