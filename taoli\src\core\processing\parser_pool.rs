/// 解析器集群模块
/// 
/// 负责低延迟并行解析交易数据（单条即处理、即发送，非阻塞）

use std::sync::Arc;
use std::sync::atomic::Ordering;
use std::thread::{self, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::time::Instant;

use crossbeam_channel::{Receiver, Sender, TryRecvError};
use compact_str::CompactString;
use log::{info, error, debug, warn};

use crate::core::stats::plugin_stats;
use crate::plugins::pump::decoders::parser::PumpParser;
use crate::plugins::bonk::BonkParser;
use crate::core::types::{RawTransaction, ParsedEvent, RealTimeMetrics, PipelineConfig, UnifiedEvent};
use crate::core::parsers::TransactionParser; // 添加这一行导入TransactionParser trait

// 插件名称常量
pub const PUMP_PLUGIN_NAME: &str = "pump";

/// 高性能解析器集群
pub struct ParserPool {
    pub workers: Vec<JoinHandle<()>>,
    config: PipelineConfig,
}

impl ParserPool {
    pub fn new(
        receiver: Receiver<RawTransaction>,
        sender: Sender<ParsedEvent>,
        config: PipelineConfig,
        metrics: Arc<RealTimeMetrics>,
        unified_sender: Option<Sender<UnifiedEvent>>,
    ) -> Self {
        let mut workers = Vec::new();
        
        info!("🔧 启动 {} 个解析器工作线程...", config.parser_threads);
        
        // 创建多个解析工作线程
        for worker_id in 0..config.parser_threads {
            let rx = receiver.clone();
            let tx = sender.clone();
            let metrics = Arc::clone(&metrics);
            let pump_parser = PumpParser::new();
            let mut bonk_parser = BonkParser::new(); // 添加BonkParser

            // 为BonkParser设置统一发布器发送端
            if let Some(ref unified_sender) = unified_sender {
                bonk_parser.set_unified_sender(unified_sender.clone());
            }
            
            let worker = thread::spawn(move || {
                // 为每个工作线程创建一个Tokio运行时
                let rt = tokio::runtime::Runtime::new().expect("无法创建Tokio运行时");
                
                // 记录线程ID，但不打印每个线程
                if worker_id == config.parser_threads - 1 {
                    info!("✅ 所有解析器工作线程已准备就绪");
                }
                
                loop {
                    // 非阻塞单条接收与处理（无睡眠、无等待）
                    match rx.try_recv() {
                        Ok(raw_tx) => {
                            // 队列深度-1（接收端已+1）
                            metrics.parse_queue_depth.fetch_sub(1, Ordering::Relaxed);

                            // 记录pump接收统计（按键判断）
                            if Self::might_be_pump_transaction(&raw_tx) {
                                plugin_stats::increment_received(PUMP_PLUGIN_NAME.to_string());
                            }

                            // 尝试使用Pump解析
                            if let Some(event) = Self::parse_pump_transaction(&pump_parser, &raw_tx, &metrics) {
                                // 成功解析Pump后立即尝试发布（非阻塞）
                                match tx.try_send(event.clone()) {
                                    Ok(_) => {
                                        metrics.parsed_count.fetch_add(1, Ordering::Relaxed);
                                        metrics.publish_queue_depth.fetch_add(1, Ordering::Relaxed);
                                        plugin_stats::increment_parsed(PUMP_PLUGIN_NAME.to_string());
                                        plugin_stats::increment_published(PUMP_PLUGIN_NAME.to_string());
                                        let publish_elapsed = event.publish_start.elapsed().as_nanos() as u64;
                                        plugin_stats::add_publish_time(PUMP_PLUGIN_NAME.to_string(), publish_elapsed);
                                    }
                                    Err(_) => {
                                        metrics.publish_queue_overflow.fetch_add(1, Ordering::Relaxed);
                                        plugin_stats::increment_publish_error(PUMP_PLUGIN_NAME.to_string());
                                    }
                                }

                                continue;
                            }

                            // 尝试使用Bonk解析（Bonk内部通过统一发布器发送）
                            if Self::can_handle_bonk(&bonk_parser, &raw_tx.account_keys) {
                                let tx_clone = Arc::new(raw_tx.clone());
                                let parse_result = rt.block_on(async { bonk_parser.parse(tx_clone).await });
                                if parse_result.is_ok() {
                                    metrics.parsed_count.fetch_add(1, Ordering::Relaxed);
                                }
                            }
                        }
                        Err(TryRecvError::Empty) => {
                            // 忙等但不sleep，避免任何人为延迟
                            std::hint::spin_loop();
                            continue;
                        }
                        Err(TryRecvError::Disconnected) => {
                            info!("解析器工作线程 {} 退出", worker_id);
                            return;
                        }
                    }
                }
            });
            
            workers.push(worker);
        }
        
        Self { workers, config }
    }
    
    /// 检查是否可能是Pump交易
    fn might_be_pump_transaction(raw_tx: &RawTransaction) -> bool {
        // 简单检查是否包含PumpFun程序ID
        let pump_program_id = crate::core::get_pump_program_id();
        raw_tx.account_keys.iter().any(|key| {
            let key_str = bs58::encode(key).into_string();
            key_str == pump_program_id
        })
    }
    
    /// 检查是否可以用Bonk解析器处理
    fn can_handle_bonk(parser: &BonkParser, account_keys: &[Vec<u8>]) -> bool {
        parser.can_handle(account_keys)
    }
    
    /// 解析单个交易（使用PumpParser）
    fn parse_pump_transaction(
        parser: &PumpParser,
        raw_tx: &RawTransaction,
        metrics: &RealTimeMetrics,
    ) -> Option<ParsedEvent> {
        let start = Instant::now();
        
        
        // 尝试解析每条日志
        let (events, errors) = parser.parse_cpi_events_from_logs(&raw_tx.logs);
        
        let parse_elapsed = start.elapsed().as_micros();
        
        // 检查并记录解析错误
        if !errors.is_empty() {
            
            metrics.parse_errors.fetch_add(errors.len() as u64, Ordering::Relaxed);
            // 记录pump解析错误
            plugin_stats::increment_parse_error(PUMP_PLUGIN_NAME.to_string());
        }
        
        if let Some(trade_event) = events.first() {
            match parser.create_extended_trade_event(trade_event.0.clone(), Some(raw_tx.signature.to_string())) {
                Ok(extended_event) => {
        
                    
                    // 创建文本格式并追加系统接收微秒时间戳
                    let mut text_format = extended_event.to_text_format();
                    text_format.push_str("\nRECV_TS_US: ");
                    text_format.push_str(&raw_tx.recv_timestamp_us.to_string());
                    
                    let duration = start.elapsed().as_nanos() as u64;
                    metrics.parse_time_total.fetch_add(duration, Ordering::Relaxed);
                    
                    // 添加插件解析耗时统计
                    plugin_stats::add_parse_time(PUMP_PLUGIN_NAME.to_string(), duration);
                    
                    // 记录发布开始时间，用于计算发布耗时
                    let publish_start = Instant::now();
                    
                    let result = Some(ParsedEvent {
                        event: extended_event,
                        json: CompactString::new(&text_format), // 使用文本格式并包含接收时间戳
                        timestamp: raw_tx.timestamp,
                        publish_start, // 添加发布开始时间
                    });
                    
                    result
                }
                Err(e) => {

                    metrics.parse_errors.fetch_add(1, Ordering::Relaxed);
                    // 记录pump解析错误
                    plugin_stats::increment_parse_error(PUMP_PLUGIN_NAME.to_string());
                    None
                }
            }
        } else {
            None
        }
    }
}