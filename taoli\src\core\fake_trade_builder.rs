/// 假交易构建器模块
/// 
/// 负责将SSE接收到的mint数据转换为假交易格式
/// 生成符合系统要求的交易数据结构

use serde::{Deserialize, Serialize};
use serde_json::json;
use compact_str::CompactString;
use std::collections::HashMap;
use rand::Rng;

use crate::core::sse_service::SseMintData;

/// 假交易数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FakeTradeData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub actual_trade_price: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
    pub recv_ts_us: u64,
}

/// 假交易构建器
#[derive(Debug, Clone)]
pub struct FakeTradeBuilder {
    /// 随机数生成器
    rng: rand::rngs::ThreadRng,
}

impl FakeTradeBuilder {
    /// 创建新的假交易构建器
    pub fn new() -> Self {
        Self {
            rng: rand::thread_rng(),
        }
    }
    
    /// 构建假交易数据
    pub fn build_fake_trade(&self, mint_data: &SseMintData, quality_type: &str) -> CompactString {
        // 根据质量类型生成不同的交易数据
        let trade_data = match quality_type {
            "high_quality" => self.build_high_quality_trade(mint_data),
            "low_quality" => self.build_low_quality_trade(mint_data),
            _ => self.build_default_trade(mint_data),
        };
        
        // 转换为JSON字符串
        let json_string = serde_json::to_string(&trade_data)
            .unwrap_or_else(|_| "{}".to_string());
        
        CompactString::new(&json_string)
    }
    
    /// 构建高质量假交易
    fn build_high_quality_trade(&self, mint_data: &SseMintData) -> FakeTradeData {
        let mut rng = rand::thread_rng();
        
        // 生成随机但合理的数值
        let base_amount = rng.gen_range(1000000000000..10000000000000); // 1-10 SOL
        let token_amount = rng.gen_range(1000000000000..10000000000000); // 1-10 tokens
        
        FakeTradeData {
            signature: self.generate_random_signature(),
            pool_state: self.generate_random_pubkey(),
            signer: self.generate_random_pubkey(),
            mint_address: mint_data.mint.clone(),
            total_base_sell: base_amount,
            virtual_base: base_amount * 2,
            virtual_quote: token_amount * 2,
            real_base_before: base_amount,
            real_quote_before: token_amount,
            real_base_after: base_amount - rng.gen_range(100000000..1000000000),
            real_quote_after: token_amount + rng.gen_range(100000000..1000000000),
            amount_in: rng.gen_range(100000000..1000000000),
            amount_out: rng.gen_range(100000000..1000000000),
            protocol_fee: rng.gen_range(100000..1000000),
            platform_fee: rng.gen_range(100000..1000000),
            share_fee: 0,
            trade_direction: "Sell".to_string(),
            pool_status: "Fund".to_string(),
            price_before: rng.gen_range(0.00000001..0.000001),
            price_after: rng.gen_range(0.00000001..0.000001),
            slippage: rng.gen_range(-1.0..1.0),
            actual_trade_price: rng.gen_range(0.00000001..0.000001),
            pool_base_vault: self.generate_random_pubkey(),
            pool_quote_vault: self.generate_random_pubkey(),
            recv_ts_us: mint_data.timestamp * 1000000 + rng.gen_range(0..1000000),
        }
    }
    
    /// 构建低质量假交易
    fn build_low_quality_trade(&self, mint_data: &SseMintData) -> FakeTradeData {
        let mut rng = rand::thread_rng();
        
        // 低质量交易通常有更大的波动
        let base_amount = rng.gen_range(100000000000..1000000000000); // 0.1-1 SOL
        let token_amount = rng.gen_range(100000000000..1000000000000); // 0.1-1 tokens
        
        FakeTradeData {
            signature: self.generate_random_signature(),
            pool_state: self.generate_random_pubkey(),
            signer: self.generate_random_pubkey(),
            mint_address: mint_data.mint.clone(),
            total_base_sell: base_amount,
            virtual_base: base_amount * 3,
            virtual_quote: token_amount * 3,
            real_base_before: base_amount,
            real_quote_before: token_amount,
            real_base_after: base_amount - rng.gen_range(10000000..100000000),
            real_quote_after: token_amount + rng.gen_range(10000000..100000000),
            amount_in: rng.gen_range(10000000..100000000),
            amount_out: rng.gen_range(10000000..100000000),
            protocol_fee: rng.gen_range(10000..100000),
            platform_fee: rng.gen_range(10000..100000),
            share_fee: 0,
            trade_direction: "Buy".to_string(),
            pool_status: "Fund".to_string(),
            price_before: rng.gen_range(0.000000001..0.0000001),
            price_after: rng.gen_range(0.000000001..0.0000001),
            slippage: rng.gen_range(-5.0..5.0),
            actual_trade_price: rng.gen_range(0.000000001..0.0000001),
            pool_base_vault: self.generate_random_pubkey(),
            pool_quote_vault: self.generate_random_pubkey(),
            recv_ts_us: mint_data.timestamp * 1000000 + rng.gen_range(0..1000000),
        }
    }
    
    /// 构建默认假交易
    fn build_default_trade(&self, mint_data: &SseMintData) -> FakeTradeData {
        self.build_high_quality_trade(mint_data)
    }
    
    /// 生成随机签名
    fn generate_random_signature(&self) -> String {
        let mut rng = rand::thread_rng();
        let mut signature = String::new();
        
        for _ in 0..88 {
            signature.push(rng.gen_range('A'..='Z'));
        }
        
        signature
    }
    
    /// 生成随机公钥
    fn generate_random_pubkey(&self) -> String {
        let mut rng = rand::thread_rng();
        let mut pubkey = String::new();
        
        for _ in 0..44 {
            pubkey.push(rng.gen_range('A'..='Z'));
        }
        
        pubkey
    }
}

impl Default for FakeTradeBuilder {
    fn default() -> Self {
        Self::new()
    }
} 