use anyhow::{anyhow, Result};
use async_trait::async_trait;
use reqwest::{header::CONTENT_TYPE, Client};
use serde::Deserialize;
use serde_json::json;
use solana_sdk::transaction::Transaction;
use std::cell::RefCell;
use base64::engine::general_purpose::STANDARD as B64_ENGINE;
use base64::Engine;
use tracing::{error, info, warn};
use rand::seq::SliceRandom;
use uuid;

use crate::config::AcceleratorConfig;
use super::{TransactionSenderBackend, rpc_sender::RpcSender};

// --- BlockRazor 硬编码的小费地址 ---
// 基于官方文档说明，BlockRazor 需要至少 1000000 lamports 的小费
const BLOCKRAZOR_TIP_ADDRESSES: [&str; 14] = [
    "FjmZZrFvhnqqb9ThCuMVnENaM3JGVuGWNyCAxRJcFpg9",
    "6No2i3aawzHsjtThw81iq1EXPJN6rh8eSJCLaYZfKDTG",
    "A9cWowVAiHe9pJfKAj3TJiN9VpbzMUq6E4kEvf5mUT22",
    "Gywj98ophM7GmkDdaWs4isqZnDdFCW7B46TXmKfvyqSm",
    "68Pwb4jS7eZATjDfhmTXgRJjCiZmw1L7Huy4HNpnxJ3o",
    "4ABhJh5rZPjv63RBJBuyWzBK3g9gWMUQdTZP2kiW31V9",
    "B2M4NG5eyZp5SBQrSdtemzk5TqVuaWGQnowGaCBt8GyM",
    "5jA59cXMKQqZAVdtopv8q3yyw9SYfiE3vUCbt7p8MfVf",
    "5YktoWygr1Bp9wiS1xtMtUki1PeYuuzuCF98tqwYxf61",
    "295Avbam4qGShBYK7E9H5Ldew4B3WyJGmgmXfiWdeeyV",
    "EDi4rSy2LZgKJX74mbLTFk4mxoTgT6F7HxxzG2HBAFyK",
    "BnGKHAC386n4Qmv9xtpBVbRaUTKixjBe3oagkPFKtoy6",
    "Dd7K2Fp7AtoN8xCghKDRmyqr5U169t48Tw5fEd3wT9mq",
    "AP6qExwrbRgBAVaehg4b5xHENX815sMabtBzUzVB4v8S",
];

thread_local! {
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct BlockRazorResponse {
    signature: String,
}

pub struct BlockRazorSender {
    client: Client,
    config: AcceleratorConfig,
    fallback_sender: RpcSender,
    skip_preflight: bool,
}

impl BlockRazorSender {
    pub fn new(client: Client, config: AcceleratorConfig, fallback_sender: RpcSender, skip_preflight: bool) -> Self {
        Self {
            client,
            config,
            fallback_sender,
            skip_preflight,
        }
    }

    pub fn get_random_tip_account() -> &'static str {
        let mut rng = rand::thread_rng();
        BLOCKRAZOR_TIP_ADDRESSES.choose(&mut rng).unwrap()
    }
}

#[async_trait]
impl TransactionSenderBackend for BlockRazorSender {
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        // 从配置中获取激活的提供商URL和Key
        let (api_url, api_key) = match self.config.get_active_provider_config() {
            Some(config) => config,
            None => {
                // 如果配置无效或未启用，直接回退
                warn!("BlockRazor 加速器未启用或提供商配置无效。将回退到标准RPC。");
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let tx_base64 = SER_BUF.with(|ser_cell| {
            B64_BUF.with(|b64_cell| {
                let mut ser_buf = ser_cell.borrow_mut();
                ser_buf.clear();
                bincode::serialize_into(&mut *ser_buf, tx)
                    .map_err(|e| anyhow!("bincode 序列化失败: {e}"))?;
                let mut b64_buf = b64_cell.borrow_mut();
                b64_buf.clear();
                B64_ENGINE.encode_string(ser_buf.as_slice(), &mut *b64_buf);
                Ok::<_, anyhow::Error>(b64_buf.clone())
            })
        })?;

        // 构造 BlockRazor 请求体格式
        let request_body = json!({
            "transaction": tx_base64,
            "mode": "fast"
        });

        // 直接使用配置的 URL
        let resp = self.client
            .post(&api_url)
            .header(CONTENT_TYPE, "application/json")
            .header("apikey", &api_key) // BlockRazor 使用 apikey header
            .json(&request_body)
            .send()
            .await;
        
        let resp = match resp {
            Ok(r) => r,
            Err(e) => {
                error!("发送到BlockRazor时发生网络错误: {}。将回退到标准RPC。", e);
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let status = resp.status();
        let text = resp.text().await?;

        if !status.is_success() {
            warn!("BlockRazor 加速器HTTP状态非成功: {} – {}。将回退到标准RPC。", status, text);
            return self.fallback_sender.send_transaction(tx).await;
        }

        // 尝试解析 BlockRazor 响应格式
        match serde_json::from_str::<BlockRazorResponse>(&text) {
            Ok(response) => {
                info!("⚡️ 交易已通过 BlockRazor 加速器提交: {}", response.signature);
                Ok(response.signature)
            }
            Err(e) => {
                error!("解析 BlockRazor JSON 失败: {}，原文: {}。回退RPC。", e, text);
                self.fallback_sender.send_transaction(tx).await
            }
        }
    }
}