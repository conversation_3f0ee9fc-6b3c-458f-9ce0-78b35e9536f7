use anyhow::{Result, Context};
use eventsource_stream::Eventsource;
use futures_util::StreamExt;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, error, warn, debug};
use uuid::Uuid;
use chrono;

/// SSE信号数据格式
#[derive(Debug, Clone, Deserialize)]
pub struct SseSignalData {
    pub mint: String,
    pub timestamp: i64,
    pub quality: String,
    pub pool: String,
}

/// 假交易数据格式 (仅用于显示)
#[derive(Debug, Clone, Serialize)]
pub struct FakeTradeData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub actual_trade_price: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
    #[serde(rename = "RECV_TS_US")]
    pub recv_ts_us: u64,
    #[serde(rename = "TYPE")]
    pub trade_type: String,
    #[serde(rename = "SIGNATURE")]
    pub tx_signature: String,
    #[serde(rename = "MINT")]
    pub token_mint: String,
}

/// SSE信号订阅服务
pub struct SseSignalSubscriber {
    high_quality_url: String,
    low_quality_url: String,
}

impl SseSignalSubscriber {
    pub fn new(
        high_quality_url: String,
        low_quality_url: String,
        _redis_client: redis::Client,  // 保留参数但不使用
        _pump_channel: String,         // 保留参数但不使用
    ) -> Self {
        Self {
            high_quality_url,
            low_quality_url,
        }
    }

    /// 启动SSE订阅服务
    pub async fn start(&self) -> Result<()> {
        info!("启动SSE信号订阅服务...");
        
        // 启动高质量信号订阅
        let high_quality_url = self.high_quality_url.clone();
        tokio::spawn(async move {
            Self::subscribe_sse_stream(high_quality_url, "high").await;
        });

        // 启动低质量信号订阅
        let low_quality_url = self.low_quality_url.clone();
        tokio::spawn(async move {
            Self::subscribe_sse_stream(low_quality_url, "low").await;
        });

        info!("SSE信号订阅服务已启动");
        
        // 保持服务运行
        std::future::pending::<()>().await;
        Ok(())
    }

    /// 订阅单个SSE流
    async fn subscribe_sse_stream(url: String, quality: &str) {
        info!("开始订阅{}质量SSE信号: {}", quality, url);
        
        loop {
            match Self::connect_and_subscribe(&url).await {
                Ok(_) => {
                    debug!("{}质量SSE连接意外断开，正在重连...", quality);
                },
                Err(_) => {
                    // 静默重试，不输出错误日志
                }
            }
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        }
    }

    /// 连接并订阅SSE流
    async fn connect_and_subscribe(url: &str) -> Result<()> {
        let client = reqwest::Client::new();
        let response = client
            .get(url)
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .send()
            .await
            .context("发送SSE请求失败")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("SSE请求失败，状态码: {}", response.status()));
        }

        let mut stream = response.bytes_stream().eventsource();

        while let Some(event) = stream.next().await {
            match event {
                Ok(event) => {
                    debug!("🔍 收到SSE事件: {:?}", event);
                    
                    if !event.data.is_empty() {
                        info!("📥 收到SSE原始数据: {}", event.data);
                        match serde_json::from_str::<SseSignalData>(&event.data) {
                            Ok(signal_data) => {
                                info!("✅ SSE信号数据解析成功: {:?}", signal_data);
                                // 同步处理数据，避免并发问题
                                Self::process_signal_data(signal_data);
                            }
                            Err(e) => {
                                warn!("❌ 解析SSE信号数据失败: {}, 原始数据: {}", e, event.data);
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("SSE流错误: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    /// 处理信号数据，只打印转换结果
    fn process_signal_data(signal_data: SseSignalData) {
        info!("🎯 开始处理SSE信号数据: {:?}", signal_data);

        if signal_data.pool == "pump" {
            // Pump格式
            info!("🔄 转换后的假交易数据:");
            info!("TYPE: Buy");
            info!("SIGNATURE: 0");
            info!("签名者地址: 1111111111111111111111111111superai");
            info!("MINT: {}", signal_data.mint);
            info!("TOKEN AMOUNT: 0");
            info!("SOL COST: 0.000000 SOL");
            info!("当前价格: 0");
            info!("创造者地址: 0");
            info!("创作者金库地址: 0");
            info!("VIRTUAL TOKEN RESERVES: 0");
            info!("VIRTUAL SOL RESERVES: 0");
            info!("REAL TOKEN RESERVES: 0");
            info!("REAL SOL RESERVES: 0");
            info!("TOKEN TOTAL SUPPLY: 未知");
            info!("COMPLETE: true");
            info!("TIME: {}", chrono::Utc::now().format("%Y-%m-%dT%H:%M:%S%.7f+08:00"));
            info!("RECV_TS_US: 0");
        } else if signal_data.pool == "bonk" {
            // Bonk格式
            info!("🔄 转换后的假交易数据:");
            info!("signature: 0");
            info!("pool_state: 0");
            info!("signer: 1111111111111111111111111111superai");
            info!("mint_address: {}", signal_data.mint);
            info!("total_base_sell: 0");
            info!("virtual_base: 0");
            info!("virtual_quote: 0");
            info!("real_base_before: 0");
            info!("real_quote_before: 0");
            info!("real_base_after: 0");
            info!("real_quote_after: 0");
            info!("amount_in: 0");
            info!("amount_out: 0");
            info!("protocol_fee: 0");
            info!("platform_fee: 0");
            info!("share_fee: 0");
            info!("trade_direction: Buy");
            info!("pool_status: Fund");
            info!("price_before: 0");
            info!("price_after: 0");
            info!("slippage: 0");
            info!("actual_trade_price: 0");
            info!("pool_base_vault: 0");
            info!("pool_quote_vault: 0");
            info!("RECV_TS_US: 0");
        }
        
        info!("✅ 信号数据处理完成");
    }

    /// 根据SSE信号创建假交易数据
    fn create_fake_trade_data(signal_data: &SseSignalData) -> FakeTradeData {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_micros() as u64;

        // 生成随机的假交易数据
        let fake_signature = Self::generate_fake_signature();
        let fake_signer = Self::generate_fake_address();
        let fake_pool_state = Self::generate_fake_address();
        let fake_base_vault = Self::generate_fake_address();
        let fake_quote_vault = Self::generate_fake_address();

        // 模拟交易参数
        let virtual_base = 1066666666666666u64;
        let virtual_quote = 28333333333u64;
        let amount_in = 1053652872982u64;
        let amount_out = 141203086u64;
        
        // 计算价格
        let price_before = virtual_quote as f64 / virtual_base as f64;
        let real_base_before = 595284012450164u64;
        let real_quote_before = 35780655532u64;
        let real_base_after = real_base_before - amount_out;
        let real_quote_after = real_quote_before + amount_in;
        let price_after = real_quote_after as f64 / real_base_after as f64;
        let slippage = (price_after - price_before) / price_before * 100.0;
        let actual_trade_price = amount_in as f64 / amount_out as f64;

        FakeTradeData {
            signature: fake_signature.clone(),
            pool_state: fake_pool_state,
            signer: fake_signer,
            mint_address: signal_data.mint.clone(),
            total_base_sell: 800000000000000,
            virtual_base,
            virtual_quote,
            real_base_before,
            real_quote_before,
            real_base_after,
            real_quote_after,
            amount_in,
            amount_out,
            protocol_fee: 357477,
            platform_fee: 1429904,
            share_fee: 0,
            trade_direction: "Buy".to_string(),
            pool_status: "Fund".to_string(),
            price_before,
            price_after,
            slippage,
            actual_trade_price,
            pool_base_vault: fake_base_vault,
            pool_quote_vault: fake_quote_vault,
            recv_ts_us: current_time,
            trade_type: "Buy".to_string(),
            tx_signature: fake_signature,
            token_mint: signal_data.mint.clone(),
        }
    }

    /// 生成假的交易签名
    fn generate_fake_signature() -> String {
        let uuid1 = Uuid::new_v4().simple().to_string();
        let uuid2 = Uuid::new_v4().simple().to_string();
        format!("{}{}fake", &uuid1[..32], &uuid2[..32])
    }

    /// 生成假的Solana地址
    fn generate_fake_address() -> String {
        let uuid = Uuid::new_v4().simple().to_string();
        format!("{}fake{}", &uuid[..28], &uuid[28..])
    }
}