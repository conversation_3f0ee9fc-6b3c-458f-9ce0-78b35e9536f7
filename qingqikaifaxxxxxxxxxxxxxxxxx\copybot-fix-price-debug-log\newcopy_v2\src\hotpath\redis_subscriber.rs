use anyhow::{anyhow, Result};
use futures_util::stream::StreamExt;
use tracing::{info, debug, warn, error, Instrument};
// 🚀 移除Mutex支持 - 改为无锁架构
use crate::hotpath::filter::Filter;
use crate::hotpath::bonk_filter::{Bonk<PERSON><PERSON><PERSON>, BonkFilterConfig, BonkFilterResult};
use crate::hotpath::bonk_builder::BonkTransactionBuilder;
use crate::hotpath::calculator::{Calculator, CalculateInput};
use crate::hotpath::transaction_builder::TransactionBuilder;
use crate::services::transaction_sender::TransactionSender;
use crate::services::ata_cache::AtaCache;
use crate::shared::types::{TradeType, TradeRecord};
use crate::shared::types::HotPathTrade;
use colored::Colorize;
use std::sync::Arc;
use arc_swap::ArcSwap;
use chrono::Local;
use dashmap::DashMap;
use tokio::sync::mpsc;
use crate::services::transaction_tracker::TransactionTracker;
use solana_sdk::signature::Signature;
use std::str::FromStr;
use crate::services::transaction_tracker::TrackRequest;
use crate::services::price_broadcast::PriceBroadcastManager;
use uuid::Uuid;
use crate::services::senders::{astralane_sender::AstralaneSender, blockrazor_sender::BlockRazorSender, flashblock_sender::FlashblockSender, oslot_sender::OslotSender};
use crate::shared::global_config::get_accelerator_config;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashSet;
use crate::config::Settings;

// 通过配置获取频道名称
fn get_pump_channel(config: &Settings) -> &str {
    &config.redis.pump_channel
}

fn get_bonk_channel(config: &Settings) -> &str {
    &config.redis.bonk_channel
}

/// 截断地址以方便显示，格式为 "前6...后4"
fn truncate_address(address: &str) -> String {
    if address.len() > 10 {
        format!("{}...{}", &address[..6], &address[address.len()-4..])
    } else {
        address.to_string()
    }
}

/// Redis订阅器，负责从Redis获取交易数据
pub struct RedisSubscriber {
    redis_url: String,
    transaction_builder: Arc<TransactionBuilder>,
    bonk_transaction_builder: Arc<BonkTransactionBuilder>, // 🚀 无锁架构，线程安全
    transaction_sender: Arc<TransactionSender>,
    #[allow(dead_code)]
    ata_cache: Arc<AtaCache>,
    processed_sigs: Arc<DashMap<String, std::time::Instant>>,
    #[allow(dead_code)]
    trade_log_tx: mpsc::Sender<TradeRecord>,
    transaction_tracker: Arc<TransactionTracker>,
    /// 向交易跟踪服务发送新交易的通道
    tracker_tx: mpsc::Sender<TrackRequest>,
    price_broadcast_manager: PriceBroadcastManager,
    /// 跟随钱包买入数量记录：(钱包地址, mint) -> 买入数量
    leader_positions: Arc<DashMap<(String, Pubkey), u64>>,
    /// 跟随钱包累计卖出数量记录：(钱包地址, mint) -> 已卖出数量
    leader_sold_amounts: Arc<DashMap<(String, Pubkey), u64>>,
}

impl RedisSubscriber {
    /// 创建新的Redis订阅器
    pub async fn new(
        redis_url: &str,
        transaction_builder: Arc<TransactionBuilder>,
        bonk_transaction_builder: Arc<BonkTransactionBuilder>, // 🚀 无锁架构，线程安全
        transaction_sender: Arc<TransactionSender>,
        ata_cache: Arc<AtaCache>,
        trade_log_tx: mpsc::Sender<TradeRecord>,
        transaction_tracker: Arc<TransactionTracker>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        price_broadcast_manager: PriceBroadcastManager,
    ) -> Result<Self> {
        info!("正在初始化Redis订阅器，URL: {}", redis_url);
        let client = redis::Client::open(redis_url)?;
        let mut con = client.get_multiplexed_async_connection().await?;
        let pong: String = redis::cmd("PING").query_async(&mut con).await?;
        if pong != "PONG" {
            return Err(anyhow!("Redis PING测试失败，收到: {}", pong));
        }
        info!("Redis连接成功");

        Ok(Self {
            redis_url: redis_url.to_string(),
            transaction_builder,
            bonk_transaction_builder, // 保存预创建的Bonk构建器
            transaction_sender,
            ata_cache,
            processed_sigs: Arc::new(DashMap::new()),
            trade_log_tx,
            transaction_tracker,
            tracker_tx,
            price_broadcast_manager,
            leader_positions: Arc::new(DashMap::new()),
            leader_sold_amounts: Arc::new(DashMap::new()),
        })
    }
    
    /// 处理Bonk筛选结果，如果通过则构建并发送交易 (对标Pump协议的直接调用方式)
    async fn process_bonk_filter_result(
        &self,
        bonk_result: &BonkFilterResult,
        trade: &HotPathTrade,
    ) -> Option<()> {
        // 只处理筛选通过且是买入交易的情况
        if !bonk_result.is_filtered {
            return None;
        }

        if trade.trade_type != TradeType::Buy {
            return None;
        }

        // 如果没有跟单金额或配置，直接跳过
        let buy_amount_sol = match bonk_result.buy_amount_sol {
            Some(amount) => amount,
            None => return None,
        };

        let config = match &bonk_result.config_used {
            Some(cfg) => cfg,
            None => return None,
        };

        // 根据加速器配置决定是否添加小费账户 (与pump协议保持一致)
        let accel_cfg = get_accelerator_config();
        let (tip_pubkey_opt, accelerator_provider) = if accel_cfg.enabled {
            match accel_cfg.provider.as_str() {
                "astralane" => (Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(), Some("astralane")),
                "blockrazor" => (Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(), Some("blockrazor")),
                "oslot" => (Pubkey::from_str(OslotSender::get_random_tip_account()).ok(), Some("oslot")),
                "flashblock" => (Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(), Some("flashblock")),
                _ => (None, None),
            }
        } else { (None, None) };

        // 🚀 构建交易 - 使用零分配高性能构建器 (基于Pump协议优化原理)
        let transaction = match self.bonk_transaction_builder.build_buy_transaction(
            trade, buy_amount_sol, config, tip_pubkey_opt, accelerator_provider
        ) {
            Ok(tx) => tx,
            Err(e) => {
                error!("Bonk交易构建失败: {}", e);
                return None;
            }
        };

        // 签名交易 (对标Pump协议的签名方式)
        let (signed_tx, signature, _blockhash) = match self.bonk_transaction_builder.sign_and_log_details(transaction).await {
            Ok(result) => result,
            Err(e) => {
                error!("Bonk交易签名失败: {}", e);
                return None;
            }
        };

        // 发送交易 (与Pump协议保持一致的发送方式)
        if let Err(e) = self.transaction_sender.send_transaction(&signed_tx).await {
            error!("Bonk交易发送失败: {}", e);
            return None;
        }

        Some(())
    }

    /// 启动订阅处理
    pub async fn start_subscription(
        &self,
        filter: Arc<ArcSwap<Filter>>,
    ) -> Result<()> {
        let client = redis::Client::open(self.redis_url.as_ref())
            .map_err(|e| anyhow!("创建Redis客户端失败: {}", e))?;

        let mut pubsub_conn = client
            .get_async_pubsub()
            .await
            .map_err(|e| anyhow!("获取PubSub连接失败: {}", e))?;

        let config = crate::config::Settings::new().map_err(|e| anyhow!("加载配置失败: {}", e))?;
        
        let pump_channel = get_pump_channel(&config);
        let bonk_channel = get_bonk_channel(&config);
        
        // 订阅Pump频道
        pubsub_conn
            .subscribe(pump_channel)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", pump_channel, e))?;

        info!("已成功订阅Redis频道: {}", pump_channel);
            
        // 订阅Bonk频道
        pubsub_conn
            .subscribe(bonk_channel)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", bonk_channel, e))?;

        info!("已成功订阅Redis频道: {}", bonk_channel);

        let mut msg_stream = pubsub_conn.on_message();
        // 初始化计算器和用户钱包公钥，它们是线程安全的，可以在消息处理中使用
        let calculator = Calculator::new();
        let user_wallet_pubkey = self.transaction_builder.get_wallet_pubkey();

        while let Some(msg) = msg_stream.next().await {
            let payload: Vec<u8> = msg.get_payload_bytes().to_vec();
            let channel = msg.get_channel_name();
            let payload_str = String::from_utf8_lossy(&payload);
            
            // 🔍 只在收到我们自己发送的假交易时记录日志
            if payload_str.contains("***********************************") {
                info!("🔍 收到假交易 - 频道: {}, 载荷大小: {} bytes", channel, payload.len());
            }
            
            // 获取配置的频道名称 - 每条消息只加载一次配置
            let current_settings = &config; // 使用之前已经加载的配置
            let bonk_channel_from_config = get_bonk_channel(current_settings);
            let pump_channel_from_config = get_pump_channel(current_settings);
            
            // 确定当前处理的协议类型
            let protocol = if channel == bonk_channel_from_config {
                "bonk".to_string()
            } else {
                "pump".to_string()
            };
            
            // 🎯 检查是否有待处理的SSE信号需要处理
            // 检查是否为假交易（只对假交易显示详细日志）
            let is_fake_trade = payload_str.contains("***********************************");

            // 先尝试从payload中提取mint地址
            let mint_from_payload = if protocol == "bonk" {
                // 从JSON格式中提取mint - bonk协议使用JSON格式
                if payload_str.contains("\"mint_address\"") {
                    payload_str.lines()
                        .find(|line| line.trim().starts_with("\"mint_address\":"))
                        .and_then(|line| {
                            line.split("\"mint_address\": \"")
                                .nth(1)
                                .and_then(|s| s.split("\"").next())
                                .map(|s| s.to_string())
                        })
                } else {
                    // 只对假交易显示警告
                    if is_fake_trade {
                        tracing::warn!("🤖 [BONK-FAKE] payload中未找到mint_address字段");
                    }
                    None
                }
            } else {
                // 从pump格式中提取mint
                payload_str.lines()
                    .find(|line| line.trim().starts_with("MINT:"))
                    .and_then(|line| {
                        line.split("MINT:").nth(1).map(|s| s.trim().to_string())
                    })
            };

            // 只对假交易显示调试信息
            if is_fake_trade {
                tracing::debug!("🤖 [{}-FAKE] 从payload提取mint: {:?}", protocol.to_uppercase(), mint_from_payload);
            }
            
            // 如果成功提取到mint且不是我们的假交易，检查待处理信号
            if let Some(mint) = mint_from_payload {
                if !payload_str.contains("***********************************") {
                    // 这是真实交易，检查是否有待处理的相同mint信号（不打印日志）
                    let client_clone = client.clone();
                    let payload_str_clone = payload_str.to_string();
                    let pump_channel = pump_channel_from_config.to_string();
                    let bonk_channel = bonk_channel_from_config.to_string();

                    tokio::spawn(async move {
                        use crate::services::sse_signal_subscriber::SseSignalSubscriber;
                        info!("🔍 [DEBUG] 开始检查mint {} 是否有待处理的SSE信号", mint);
                        let found_signal = SseSignalSubscriber::check_and_create_fake_trade(
                            &mint,
                            &payload_str_clone,
                            &client_clone,
                            &pump_channel,
                            &bonk_channel
                        ).await;

                        if found_signal {
                            info!("🎯 [DEBUG] 成功处理mint {} 的SSE信号并创建假交易", mint);
                        } else {
                            debug!("🔍 [DEBUG] mint {} 没有待处理的SSE信号", mint);
                        }
                    });
                } else {
                    // 只对假交易显示跳过日志
                    tracing::info!("🤖 [{}-FAKE] 跳过假交易数据，mint: {}", protocol.to_uppercase(), mint);
                }
            } else {
                // 只对假交易显示警告
                if is_fake_trade {
                    tracing::warn!("🤖 [{}-FAKE] 无法从payload中提取mint地址", protocol.to_uppercase());
                    if protocol == "bonk" {
                        // 为bonk假交易添加更详细的调试信息
                        tracing::warn!("🤖 [BONK-FAKE] payload内容: {}",
                                      if payload_str.len() > 300 { &payload_str[..300] } else { &payload_str });
                    }
                }
            }
            
            // 解析交易数据
            let mut trades = if channel == bonk_channel_from_config {
                // 使用bonk解析器
                crate::protocols::bonk::parser::parse_trades_from_redis_bytes(&payload, &user_wallet_pubkey)
            } else {
                // 使用pump解析器（默认）
                crate::protocols::pump::parser::parse_trades_from_redis_bytes(&payload, &user_wallet_pubkey)
            };

            // 只对假交易显示解析结果
            if is_fake_trade && !trades.is_empty() {
                info!("🤖 [{}-FAKE] 解析假交易结果: 找到 {} 笔交易", protocol.to_uppercase(), trades.len());
            }
            
            if !trades.is_empty() {
                
                // 遍历并独立处理每一笔交易
                for i in 0..trades.len() {
                    // 获取交易信息用于日志记录
                    let trade_mint = trades[i].mint_pubkey;
                    let trade_signer = &trades[i].signer;
                    let trade_type = &trades[i].trade_type;
                    let trades_count = trades.len();
                    
                    // 只对假交易记录详细日志
                    if trade_signer == "***********************************" {
                        info!("🔍 处理假交易 {}/{} - MINT: {}, 类型: {:?}", 
                              i + 1, trades_count, trade_mint, trade_type);
                    }
                    
                    // 获取交易的可变引用
                    let trade = &mut trades[i];
                    
                    // --- 为交易分配唯一ID ---
                    trade.trade_id = Uuid::new_v4().to_string();
                    
                    // --- 设置协议类型 ---
                    trade.protocol = Some(protocol.clone());
                    
                    // --- 签名去重 (早期退出) ---
                    if !trade.signature.is_empty() {
                        const TTL_SECS: u64 = 120;
                        let now = std::time::Instant::now();
                        if let Some(entry) = self.processed_sigs.get(&trade.signature) {
                            if now.duration_since(*entry.value()) < std::time::Duration::from_secs(TTL_SECS) {
                                // 只对假交易显示跳过日志
                                if trade.signer == "***********************************" {
                                    info!("🔍 跳过重复假交易签名: {}", trade.signature);
                                }
                                continue; // 重复签名且未过期，跳过
                            }
                        }
                        // 写入/刷新签名时间
                        self.processed_sigs.insert(trade.signature.clone(), now);
                    }

                    // --- 新架构：广播价格更新 ---
                    self.price_broadcast_manager.broadcast(trade.mint_pubkey, trade.price);

                    // 如果是我们自己钱包的真实交易，则通知跟踪器确认
                    if trade.signer == user_wallet_pubkey.to_string() {
                         if let Ok(_signature) = Signature::from_str(&trade.signature) {
                            let tracker_clone = self.transaction_tracker.clone();
                            let trade_for_confirmation = trade.clone();
                            tokio::spawn(async move {
                                tracker_clone.confirm_transaction(trade_for_confirmation).await;
                            });
                        }
                    }

                    // 无锁读取: 获取筛选器配置的当前快照
                    let current_filter = filter.load();

                    // 只对假交易显示筛选检查日志
                    if trade.signer == "***********************************" {
                        info!("🔍 开始筛选检查假交易 - 协议: {}", protocol);
                    }

                    // --- Bonk协议专用筛选处理 ---
                    // 修改处理Bonk协议专用筛选部分，实现构建和发送功能
                    if protocol == "bonk" {
                        // 只对假交易显示bonk筛选日志
                        if trade.signer == "***********************************" {
                            info!("🔍 Bonk协议筛选 - 检查钱包是否被跟踪");
                        }
                        // 直接使用现有筛选器进行bonk筛选
                        if let Some(_config) = current_filter.get_config_if_tracked(&trade) {
                            // 只对假交易显示找到配置的日志
                            if trade.signer == "***********************************" {
                                info!("🔍 Bonk - 找到匹配的钱包配置，开始bonk筛选");
                            }
                            // 创建Bonk筛选器记录详细信息
                            let bonk_config = BonkFilterConfig {
                                enabled: true,
                                raydium_launchpad_enabled: true,
                                min_liquidity_sol: None, // 使用配置中的限制
                                max_liquidity_sol: None, // 使用配置中的限制
                                blocked_pools: HashSet::new(),
                                allowed_pools: HashSet::new(),
                                min_trade_amount_sol: None, // 使用配置中的限制
                                max_trade_amount_sol: None, // 使用配置中的限制
                            };
                            
                            let bonk_filter = BonkFilter::new(current_filter.config.clone(), bonk_config);
                            let bonk_result = bonk_filter.filter_bonk_trade(&trade);
                            
                            // 处理Bonk筛选结果，如果通过则构建并发送交易
                            if bonk_result.is_filtered && trade.trade_type == TradeType::Buy {
                                if let Some(buy_amount_sol) = bonk_result.buy_amount_sol {
                                    if let Some(config) = &bonk_result.config_used {
                                        
                                        // 添加与PUMP一致的"收到买入交易"日志
                                        let ui_token_amount = trade.token_amount / 10u64.pow(6);
                                        let ts = Local::now().format("%H:%M:%S.%3f");
                                        info!(
                                            "{} 收到bonk买入交易 (原始: {} SOL / {} Tokens, 来自钱包: {}, 签名: {})",
                                            ts,
                                            trade.sol_cost.to_string().cyan(),
                                            ui_token_amount.to_string().cyan(),
                                            truncate_address(&trade.signer).yellow(),
                                            trade.signature.blue()
                                        );
                                        
                                        // --- 构建交易开始 ---
                                        
                                        // 使用预创建的Bonk构建器 (与Pump协议保持一致)

                                        // 获取钱包公钥
                                        let wallet_pubkey = self.transaction_builder.get_wallet_pubkey();
                                        
                                        // 根据加速器配置决定是否添加小费账户 (与pump协议保持一致)
                                        let accel_cfg = get_accelerator_config();
                                        let (tip_pubkey_opt, accelerator_provider) = if accel_cfg.enabled {
                                            match accel_cfg.provider.as_str() {
                                                "astralane" => (Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(), Some("astralane")),
                                                "blockrazor" => (Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(), Some("blockrazor")),
                                                "oslot" => (Pubkey::from_str(OslotSender::get_random_tip_account()).ok(), Some("oslot")),
                                                "flashblock" => (Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(), Some("flashblock")),
                                                _ => (None, None),
                                            }
                                        } else { (None, None) };
                                        
                                        // 🚀 构建交易 - 使用零分配高性能构建器 (基于Pump协议优化原理)
                                        let transaction = match self.bonk_transaction_builder.build_buy_transaction(&trade, buy_amount_sol, config, tip_pubkey_opt, accelerator_provider) {
                                            Ok(tx) => tx,
                                            Err(e) => {
                                                error!("Bonk交易构建失败: {}", e);
                                                continue;
                                            }
                                        };

                                        // 签名交易 (对标Pump协议的签名方式)
                                        let (signed_tx, signature, _blockhash) = match self.bonk_transaction_builder.sign_and_log_details(transaction).await {
                                            Ok(result) => result,
                                            Err(e) => {
                                                error!("Bonk交易签名失败: {}", e);
                                                continue;
                                            }
                                        };

                                        // 记录构建结果日志
                                        let mode_text = match config.follow_mode {
                                            crate::shared::types::FollowMode::Percentage => "百分比跟单",
                                            crate::shared::types::FollowMode::FixedAmount => "固定金额跟单",
                                        };
                                        
                                        // 添加与PUMP一致的买入日志
                                        let target_token_amount = (buy_amount_sol / trade.price * 1_000_000.0) as u64;
                                        warn!(
                                            "{}: 目标代币 {}, 最大花费 {} SOL",
                                            mode_text,
                                            target_token_amount.to_string().green(),
                                            buy_amount_sol.to_string().green(),
                                        );
                                        
                                        // --- 异步发送交易 (与Pump协议一致) ---
                                        let sender_clone = self.transaction_sender.clone();
                                        let tracker_tx_clone = self.tracker_tx.clone();
                                        let wallet_pubkey = self.bonk_transaction_builder.get_wallet_pubkey();
                                        let trade_clone = trade.clone();
                                        let config_clone = config.clone();
                                        
                                        tokio::spawn(async move {
                                            let send_span = tracing::info_span!("Bonk交易发送");
                                            let final_signature_str = async {
                                                info!("开始提交到加速器...");
                                                let send_start = std::time::Instant::now();
                                                let send_result = sender_clone.send_transaction(&signed_tx).await;
                                                let send_duration = send_start.elapsed();
                                                match send_result {
                                                    Ok(sig_str) => {
                                                        info!("提交成功，收到签名: {} | 网络耗时: {:?}", sig_str, send_duration);
                                                        sig_str
                                                    },
                                                    Err(e) => {
                                                        error!("提交失败: {} | 网络耗时: {:?}", e, send_duration);
                                                        signature.to_string() // fallback signature
                                                    }
                                                }
                                            }.instrument(send_span).await;
                                            
                                            // 跟踪交易
                                            let track_req = TrackRequest {
                                                trade_type: TradeType::Buy,
                                                signature: Signature::from_str(&final_signature_str).unwrap_or(signature),
                                                mint: trade_clone.mint_pubkey.to_string(),
                                                sol_amount: buy_amount_sol,
                                                token_amount: (buy_amount_sol / trade_clone.price * 1_000_000.0) as u64, // 估算的token数量
                                                user_wallet: wallet_pubkey.to_string(),
                                                entry_sol_amount_usd: None,
                                                trade_info: Some(trade_clone),
                                                wallet_config: Some(config_clone),
                                                executor_type: "bonk".to_string(), // bonk买入交易
                                            };
                                            
                                            if let Err(e) = tracker_tx_clone.send(track_req).await {
                                                error!("无法将Bonk交易发送到跟踪通道: {}", e);
                                            }
                                        });
                                    }
                                }
                            }
                            
                            // 已单独处理Bonk交易，跳过通用流程
                            continue;
                        } else {
                            // 只对假交易显示跳过日志
                            if trade.signer == "***********************************" {
                                info!("🔍 Bonk - 钱包不在跟踪列表中，跳过处理");
                            }
                            continue;
                        }
                    }

                    // --- 步骤1：通用筛选 ---
                    // 检查这笔交易是否来自我们跟踪的钱包，无论买卖
                    if let Some(config) = current_filter.get_config_if_tracked(&trade) {
                        // 只对假交易显示pump找到配置日志
                        if trade.signer == "***********************************" {
                            info!("🔍 Pump - 找到匹配的钱包配置: {}", config.wallet_address);
                        }
                        
                        // --- 步骤2：按类型分流 ---
                        match trade.trade_type {
                            TradeType::Sell => {
                                // 对于卖出交易，只记录日志。持仓同步逻辑已移除。
                                let decimals = 6;
                                let ui_token_amount = trade.token_amount as f64 / 10f64.powi(decimals);
                                
                                let token_str = format!("{} tokens", ui_token_amount).red();
                                let sol_str = format!("{:.6} SOL", trade.sol_cost).red();

                                info!(
                                    "观察到卖出: {}, 获得 {} (来自跟踪的钱包: {}, 签名: {})",
                                    token_str,
                                    sol_str,
                                    trade.signer,
                                    trade.signature
                                );

                                // 🚀 跟卖功能 - 基于累计卖出数量的正确跟卖逻辑
                                if config.follow_sell_enabled {
                                    let leader_key = (trade.signer.clone(), trade.mint_pubkey);
                                    if let Some(leader_buy_amount) = self.leader_positions.get(&leader_key) {
                                        // 更新跟随钱包的累计卖出数量
                                        let current_sold = self.leader_sold_amounts.get(&leader_key).map(|v| *v.value()).unwrap_or(0);
                                        let new_total_sold = current_sold + trade.token_amount;
                                        self.leader_sold_amounts.insert(leader_key.clone(), new_total_sold);
                                        
                                        // 计算跟随钱包的累计卖出比例
                                        let total_sell_percentage = (new_total_sold as f64 / *leader_buy_amount.value() as f64) * 100.0;
                                        
                                        info!("[跟卖触发] {}累计卖出{:.1}%", truncate_address(&trade.signer), total_sell_percentage);
                                        
                                        // 先触发价格广播，确保Actor能收到最新价格
                                        self.price_broadcast_manager.broadcast(trade.mint_pubkey, trade.price);
                                        
                                        // 传递累计卖出比例给TradeLifecycleActor
                                        let follow_sell_signal = 1000000 + (total_sell_percentage * 100.0) as u64;
                                        
                                        // 通过revoked_mints触发所有该mint的TradeLifecycleActor执行跟卖策略
                                        let revoked_entry = self.transaction_tracker.get_revoked_mints();
                                        revoked_entry.insert(trade.mint_pubkey, follow_sell_signal);
                                    } else {
                                        info!("[跟卖跳过] {}卖出但无买入记录", truncate_address(&trade.signer));
                                    }
                                }
                            }
                            TradeType::Buy => {
                                // 对于买入交易，我们进行详细的计算和决策
                                if let Some(buy_amount_sol) = current_filter.check_and_get_buy_details(&trade, config) {
                                    
                                    // --- 纯计算逻辑 ---
                                    let input = CalculateInput {
                                        buy_amount_sol,
                                        trade_price: trade.price,
                                        config,
                                    };
                                    let calculation_result = calculator.calculate(input);
                                    
                                    if calculation_result.should_trade {
                                        
                                        // --- 在构建前，使用钱包配置更新交易信息 ---
                                        // 计算并设置正确的滑点 (基点)
                                        trade.slippage_bps = (config.slippage_percentage * 100.0) as u64;

                                        // --- 构建交易并计时 ---
                                        let ui_token_amount = trade.token_amount / 10u64.pow(6);

                                        let ts = Local::now().format("%H:%M:%S.%3f");
                                        info!(
                                            "{} 收到pump买入交易 (原始: {} SOL / {} Tokens, 来自钱包: {}, 签名: {})",
                                            ts,
                                            trade.sol_cost.to_string().cyan(),
                                            ui_token_amount.to_string().cyan(),
                                            truncate_address(&trade.signer).yellow(),
                                            trade.signature.blue()
                                        );

                                        // 🚀 记录跟随钱包的买入数量（用于后续跟卖）
                                        let leader_key = (trade.signer.clone(), trade.mint_pubkey);
                                        self.leader_positions.insert(leader_key, trade.token_amount);
                                        if config.follow_sell_enabled {
                                            info!("[跟卖记录] {}买入{}", truncate_address(&trade.signer), trade.token_amount);
                                        }
                                        
                                        // 将Token数量从UI单位转换为最小单位（假设decimals=6）
                                        let target_token_amount_raw = calculation_result.min_expected_token_amount_out * 1_000_000u64;

                                        // --- 核心热路径执行 ---
                                        // 根据加速器配置决定是否添加小费账户
                                        let accel_cfg = get_accelerator_config();
                                        let tip_pubkey_opt = if accel_cfg.enabled {
                                            match accel_cfg.provider.as_str() {
                                                "astralane" => Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(),
                                                "blockrazor" => Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(),
                                                "oslot" => Pubkey::from_str(OslotSender::get_random_tip_account()).ok(),
                                                "flashblock" => Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(),
                                                _ => None,
                                            }
                                        } else { None };

                                        // We must have a tip account to proceed with this optimized builder
                                        if let Some(tip_pubkey) = tip_pubkey_opt {
                                            let max_sol_lamports = (calculation_result.final_buy_amount_sol * 1_000_000_000.0) as u64;
                                            let unsigned_tx_result = self.transaction_builder.build_buy_transaction(
                                                &trade,
                                                target_token_amount_raw,
                                                max_sol_lamports,
                                                config,
                                                tip_pubkey,
                                                if accel_cfg.enabled { Some(accel_cfg.provider.as_str()) } else { None },
                                            );

                                            // Log calculation results with mode-specific text
                                            let mode_text = match config.follow_mode {
                                                crate::shared::types::FollowMode::Percentage => "百分比跟单",
                                                crate::shared::types::FollowMode::FixedAmount => "固定金额跟单",
                                            };
                                            warn!(
                                                "{}: 目标代币 {}, 最大花费 {} SOL",
                                                mode_text,
                                                calculation_result.min_expected_token_amount_out.to_string().green(),
                                                calculation_result.final_buy_amount_sol.to_string().green(),
                                            );

                                            if let Ok(tx) = unsigned_tx_result {
                                                // Sign and log details
                                                let (signed_tx, signature_from_builder, _recent_blockhash) = self.transaction_builder.sign_and_log_details(tx).await.unwrap();

                                                // Send the transaction
                                                let send_span = tracing::info_span!("Pump交易发送");
                                                let final_signature_str = async {
                                                    info!("开始提交到加速器...");
                                                    let send_start = std::time::Instant::now();
                                                    let send_result = self.transaction_sender.send_transaction(&signed_tx).await;
                                                    let send_duration = send_start.elapsed();
                                                    
                                                    match send_result {
                                                        Ok(sig_str) => {
                                                            info!("提交成功，收到签名: {} | 网络耗时: {:?}", sig_str, send_duration);
                                                            sig_str
                                                        },
                                                        Err(e) => {
                                                            error!("提交失败: {} | 网络耗时: {:?}", e, send_duration);
                                                            signature_from_builder.to_string()
                                                        }
                                                    }
                                                }.instrument(send_span).await;


                                                // 无论发送成功与否，都开始跟踪
                                                let track_req = TrackRequest {
                                                    trade_type: TradeType::Buy,
                                                    signature: Signature::from_str(&final_signature_str).unwrap_or(signature_from_builder),
                                                    mint: trade.mint_pubkey.to_string(),
                                                    sol_amount: trade.sol_cost,
                                                    token_amount: trade.token_amount,
                                                    user_wallet: self.transaction_builder.get_wallet_pubkey().to_string(),
                                                    entry_sol_amount_usd: None,
                                                    trade_info: Some(trade.clone()),
                                                    wallet_config: Some(config.clone()),
                                                    executor_type: "pump".to_string(), // pump买入交易
                                                };
                                                if let Err(e) = self.tracker_tx.send(track_req).await {
                                                    error!("无法将交易发送到跟踪通道: {}", e);
                                                }
                                                
                                            } else if let Err(e) = unsigned_tx_result {
                                                error!("构建交易失败: {}", e);
                                            }
                                        } else {
                                            warn!("加速器已启用但未找到有效的小费账户，跳过交易构建。");
                                        }

                                    } else {
                                        debug!("交易在计算阶段被拒绝 (签名者: {}), 原因: {}", trade.signer, calculation_result.reason);
                                    }
                                } else {
                                    // 交易在购买细节检查阶段被拒绝
                                    debug!(
                                        "购买交易被拒绝 (签名者: {}, MINT: {}), 价格: {}",
                                        trade.signer, trade.mint_pubkey.to_string(), trade.price
                                    );
                                }
                            }
                            TradeType::Unknown => {
                                // 忽略未知类型的交易
                            }
                        }
                    }
                    // 如果 `get_config_if_tracked` 返回 None，则交易会被安静地忽略
                    else {
                        // 只对假交易显示跳过日志
                        if trade.signer == "***********************************" {
                            info!("🔍 交易被跳过 - 钱包不在跟踪列表中: {}", trade.signer);
                        }
                    }
                }
            } else {
                // 只对假交易显示没有解析到数据的日志
                if payload_str.contains("***********************************") {
                    info!("🔍 没有解析到任何假交易数据");
                }
            }
        }
        Ok(())
    }
} 