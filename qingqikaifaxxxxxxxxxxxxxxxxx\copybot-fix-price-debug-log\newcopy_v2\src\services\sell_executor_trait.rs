use std::time::Duration;
use anyhow::Result;
use async_trait::async_trait;
use solana_sdk::signature::Signature;

use crate::shared::types::{HotPathTrade, WalletConfig};

/// 统一的卖出执行器接口，支持不同的协议
#[async_trait]
pub trait SellExecutorTrait: Send + Sync {
    /// 执行卖出交易
    async fn execute_sell(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
    ) -> Result<(), anyhow::Error>;

    /// 执行卖出交易，支持自定义滑点（用于重试）
    async fn execute_sell_with_slippage(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
        custom_slippage: Option<f64>,
    ) -> Result<(), anyhow::Error>;

    /// 构建并发送卖出交易，返回签名但不创建跟踪记录（用于重试）
    async fn build_and_send_sell_transaction(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        current_price: f64,
        custom_slippage: Option<f64>,
        reason: &str,
    ) -> Result<Signature, anyhow::Error>;
}