[package]
name = "newcopy_v2"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
bytes = "1.10.1"
futures-util = "0.3.30"
redis = { version = "0.25.3", features = ["tokio-comp"] }
serde = "1.0.203"
serde_derive = "1.0.203"
thiserror = "1.0"
tokio = { version = "1.37.0", features = ["full"] }
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "time", "local-time"] }
time = { version = "0.3", features = ["formatting", "macros"] }
colored = "2.1.0"
lexical-core = "0.8.5"
memchr = "2.7.2"
base64 = "0.22.1"
arc-swap = "1.6"
serde_json = "1.0"
chrono = "0.4"
solana-sdk = "=1.16.27"
spl-associated-token-account = { version = "1.1.1", features = ["no-entrypoint"] }
spl-token = { version = "3.5.0", features = ["no-entrypoint"] }
bincode = "1.3.3"
bs58 = "0.5.0"
hex = "0.4.3"
reqwest = { version = "0.11", features = ["json", "rustls-tls", "stream"] }
eventsource-stream = "0.2.3"
uuid = { version = "1.10.0", features = ["v4"] }
solana-client = "1.16.27"
solana-transaction-status = "1.16.27"

dashmap = "5.5"
async-stream = "0.3.5"
once_cell = "1.19.0"
rand = "0.8.5"
lazy_static = "1.4.0"

# gRPC 和服务依赖
yellowstone-grpc-client = { git = "https://github.com/rpcpool/yellowstone-grpc-mango.git", branch = "v1.16-mango" }
yellowstone-grpc-proto = { git = "https://github.com/rpcpool/yellowstone-grpc-mango.git", branch = "v1.16-mango" }
tonic = { version = "=0.10.2", features = ["tls", "transport"] }
prost = "=0.12.6"
toml = "0.8.13"

axum = "0.7.5"
tower-http = { version = "0.5.2", features = ["cors"] }

# 强制使用旧版本以兼容 stable toolchain
pkcs8 = "=0.7.1"
spki = "=0.4.1"
ahash = "=0.8.4"
base64ct = "=1.6.0"

async-trait = "0.1"
tracing-appender = "0.2.0"

[dev-dependencies]
criterion = "0.5.1"

[target.'cfg(not(target_env = "msvc"))'.build-dependencies]
protobuf-src = "1.1.0"

