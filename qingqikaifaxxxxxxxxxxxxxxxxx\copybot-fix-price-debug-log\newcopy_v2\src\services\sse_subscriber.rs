use anyhow::{anyhow, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use tokio::time::{sleep, Duration};
use tracing::{info, warn, error, debug};
use uuid::Uuid;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use chrono::{DateTime, Utc};
use futures_util::StreamExt;

use crate::config::SseSubscriptionConfig;
use crate::shared::types::{HotPathTrade, TradeType};

/// SSE信号数据结构
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SseSignal {
    /// 代币mint地址
    pub mint: String,
    /// 时间戳
    pub timestamp: i64,
    /// 信号质量：high 或 low
    pub quality: String,
    /// 池类型：pump 或其他
    pub pool: String,
}

/// SSE订阅器，负责订阅外部信号源并转换为假交易数据
pub struct SseSubscriber {
    /// HTTP客户端
    client: Client,
    /// 配置信息
    config: SseSubscriptionConfig,
    /// 发送假交易数据的通道
    fake_trade_tx: mpsc::Sender<HotPathTrade>,
}

impl SseSubscriber {
    /// 创建新的SSE订阅器
    pub fn new(
        config: SseSubscriptionConfig,
        fake_trade_tx: mpsc::Sender<HotPathTrade>,
    ) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.connection_timeout_secs))
            .build()
            .map_err(|e| anyhow!("创建HTTP客户端失败: {}", e))?;

        info!("SSE订阅器初始化完成");
        info!("高质量信号端点: {}", config.high_quality_endpoint);
        info!("低质量信号端点: {}", config.low_quality_endpoint);

        Ok(Self {
            client,
            config,
            fake_trade_tx,
        })
    }

    /// 启动SSE订阅服务
    pub async fn start(&self) -> Result<()> {
        info!("启动SSE订阅服务...");

        // 启动高质量信号订阅
        let high_quality_task = self.subscribe_endpoint(
            self.config.high_quality_endpoint.clone(),
            "high".to_string(),
        );

        // 启动低质量信号订阅
        let low_quality_task = self.subscribe_endpoint(
            self.config.low_quality_endpoint.clone(),
            "low".to_string(),
        );

        // 并行运行两个订阅任务
        tokio::select! {
            result = high_quality_task => {
                error!("高质量信号订阅任务结束: {:?}", result);
                result
            }
            result = low_quality_task => {
                error!("低质量信号订阅任务结束: {:?}", result);
                result
            }
        }
    }

    /// 订阅单个端点
    async fn subscribe_endpoint(&self, endpoint: String, quality_type: String) -> Result<()> {
        loop {
            info!("正在连接{}质量信号端点: {}", quality_type, endpoint);
            
            match self.connect_and_listen(&endpoint, &quality_type).await {
                Ok(_) => {
                    warn!("{}质量信号连接意外断开，准备重连", quality_type);
                }
                Err(e) => {
                    error!("{}质量信号连接失败: {}", quality_type, e);
                }
            }

            // 等待重连间隔
            info!("{}秒后重连{}质量信号端点", self.config.reconnect_interval_secs, quality_type);
            sleep(Duration::from_secs(self.config.reconnect_interval_secs)).await;
        }
    }

    /// 连接并监听SSE流
    async fn connect_and_listen(&self, endpoint: &str, quality_type: &str) -> Result<()> {
        let response = self.client
            .get(endpoint)
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .send()
            .await
            .map_err(|e| anyhow!("连接SSE端点失败: {}", e))?;

        if !response.status().is_success() {
            return Err(anyhow!("SSE端点返回错误状态: {}", response.status()));
        }

        info!("成功连接{}质量信号端点", quality_type);

        let mut stream = response.bytes_stream();
        let mut buffer = String::new();

        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| anyhow!("读取SSE数据失败: {}", e))?;
            let chunk_str = String::from_utf8_lossy(&chunk);
            buffer.push_str(&chunk_str);

            // 处理完整的SSE消息
            while let Some(pos) = buffer.find("\n\n") {
                let message = buffer[..pos].to_string();
                buffer = buffer[pos + 2..].to_string();

                if let Err(e) = self.process_sse_message(&message, quality_type).await {
                    warn!("处理{}质量SSE消息失败: {}", quality_type, e);
                }
            }
        }

        Ok(())
    }

    /// 处理SSE消息
    async fn process_sse_message(&self, message: &str, quality_type: &str) -> Result<()> {
        // 解析SSE消息格式
        for line in message.lines() {
            if line.starts_with("data: ") {
                let data = &line[6..]; // 移除 "data: " 前缀
                
                // 尝试解析JSON数据
                match serde_json::from_str::<SseSignal>(data) {
                    Ok(signal) => {
                        debug!("收到{}质量信号: mint={}, pool={}", quality_type, signal.mint, signal.pool);
                        
                        // 转换为假交易数据
                        if let Ok(fake_trade) = self.convert_signal_to_fake_trade(&signal).await {
                            // 发送假交易到处理通道
                            if let Err(e) = self.fake_trade_tx.send(fake_trade).await {
                                error!("发送假交易数据失败: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        debug!("解析{}质量信号JSON失败: {} | 数据: {}", quality_type, e, data);
                    }
                }
            }
        }

        Ok(())
    }

    /// 将SSE信号转换为假交易数据
    async fn convert_signal_to_fake_trade(&self, signal: &SseSignal) -> Result<HotPathTrade> {
        // 解析mint地址
        let mint_pubkey = Pubkey::from_str(&signal.mint)
            .map_err(|e| anyhow!("解析mint地址失败: {}", e))?;

        // 生成假的账户地址（这些在实际使用中需要正确计算）
        let creator_vault_pubkey = Pubkey::from_str("7aHyPrNM3GZYFqBhWRTZ6Si7QVJhi1kezm4wbCmDkfQ1")
            .unwrap_or_default();
        let bonding_curve_pubkey = Pubkey::from_str("JDyQcobgLNmrXhJrRAMtUjrvQgLDVPKT45HetWx68pn6")
            .unwrap_or_default();
        let associated_bonding_curve = Pubkey::from_str("oYKJcjGon81kwvta5vm7a4TyRsUbbAybSFGLuC1Kiow")
            .unwrap_or_default();
        let user_ata = Pubkey::from_str("F6rwx99NKvQg4k7uN5ZjbGiSo9aC2TUqWp9SVWtsibze")
            .unwrap_or_default();

        // 生成假的交易数据
        let fake_trade = HotPathTrade {
            trade_id: Uuid::new_v4().to_string(),
            trade_type: TradeType::Buy, // 默认为买入
            signature: self.generate_fake_signature(),
            sol_cost: 0.49, // 假的SOL成本
            token_amount: 6251982511091, // 假的token数量
            signer: "3P5JukN2xg33mQxbiMwHEdAn5LBxY4arU3zWNd6poj7s".to_string(), // 假的签名者地址
            price: 7.914346098564656e-8, // 假的价格
            protocol: Some(signal.pool.clone()), // 使用信号中的池类型作为协议
            mint_pubkey,
            creator_vault_pubkey,
            bonding_curve_pubkey,
            associated_bonding_curve,
            user_ata,
            slippage_bps: 500, // 默认5%滑点
        };

        info!("转换SSE信号为假交易: mint={}, 质量={}, 协议={}",
              signal.mint, signal.quality, signal.pool);

        Ok(fake_trade)
    }

    /// 生成假的交易签名
    fn generate_fake_signature(&self) -> String {
        // 生成64字符的假签名（模拟真实的Solana交易签名格式）
        let chars: Vec<char> = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz".chars().collect();
        let mut signature = String::with_capacity(88); // Base58编码的签名长度通常是88字符

        use rand::Rng;
        let mut rng = rand::thread_rng();
        for _ in 0..88 {
            let idx = rng.gen_range(0..chars.len());
            signature.push(chars[idx]);
        }

        signature
    }
}
