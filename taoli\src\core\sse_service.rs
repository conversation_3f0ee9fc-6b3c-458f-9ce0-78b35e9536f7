/// SSE服务模块
/// 
/// 负责订阅SSE端点，接收高质量和低质量信号数据
/// 并将数据转换为假交易格式发送到Redis发布系统

use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use log::{info, error, warn, debug};
use serde::{Deserialize, Serialize};
use crossbeam_channel::Sender;
use anyhow::Result;

use crate::core::types::{UnifiedEvent, get_config};
use crate::core::fake_trade_builder::FakeTradeBuilder;

/// SSE数据格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SseMintData {
    pub mint: String,
    pub timestamp: u64,
    pub quality: String,
    pub pool: String,
}

/// SSE服务配置
#[derive(Debug, Clone)]
pub struct SseConfig {
    pub high_quality_endpoint: String,
    pub low_quality_endpoint: String,
    pub reconnect_interval: u64,
    pub timeout: u64,
}

impl From<&crate::core::types::AppConfig> for SseConfig {
    fn from(config: &crate::core::types::AppConfig) -> Self {
        Self {
            high_quality_endpoint: config.sse.high_quality_endpoint.clone(),
            low_quality_endpoint: config.sse.low_quality_endpoint.clone(),
            reconnect_interval: config.sse.reconnect_interval,
            timeout: config.sse.timeout,
        }
    }
}

/// SSE服务
pub struct SseService {
    config: SseConfig,
    fake_trade_builder: FakeTradeBuilder,
    unified_sender: Sender<UnifiedEvent>,
}

impl SseService {
    /// 创建新的SSE服务
    pub fn new(unified_sender: Sender<UnifiedEvent>) -> Self {
        let config = SseConfig::from(&get_config().app_config);
        let fake_trade_builder = FakeTradeBuilder::new();
        
        Self {
            config,
            fake_trade_builder,
            unified_sender,
        }
    }
    
    /// 启动SSE服务
    pub async fn start(&self) -> Result<()> {
        info!("🚀 启动SSE订阅服务...");
        
        // 启动高质量信号订阅
        let high_quality_sender = self.unified_sender.clone();
        let high_config = self.config.clone();
        let high_builder = self.fake_trade_builder.clone();
        
        tokio::spawn(async move {
            if let Err(e) = Self::subscribe_to_endpoint(
                &high_config.high_quality_endpoint,
                "high_quality",
                &high_quality_sender,
                &high_builder,
                high_config.reconnect_interval,
            ).await {
                error!("高质量信号SSE订阅失败: {}", e);
            }
        });
        
        // 启动低质量信号订阅
        let low_quality_sender = self.unified_sender.clone();
        let low_config = self.config.clone();
        let low_builder = self.fake_trade_builder.clone();
        
        tokio::spawn(async move {
            if let Err(e) = Self::subscribe_to_endpoint(
                &low_config.low_quality_endpoint,
                "low_quality",
                &low_quality_sender,
                &low_builder,
                low_config.reconnect_interval,
            ).await {
                error!("低质量信号SSE订阅失败: {}", e);
            }
        });
        
        info!("✅ SSE服务启动完成，已订阅高质量和低质量信号端点");
        Ok(())
    }
    
    /// 订阅指定端点
    async fn subscribe_to_endpoint(
        endpoint: &str,
        quality_type: &str,
        sender: &Sender<UnifiedEvent>,
        builder: &FakeTradeBuilder,
        reconnect_interval: u64,
    ) -> Result<()> {
        let mut retry_count = 0;
        let max_retries = 10;
        
        loop {
            info!("🔗 连接到{} SSE端点: {}", quality_type, endpoint);
            
            match Self::connect_and_subscribe(endpoint, quality_type, sender, builder).await {
                Ok(_) => {
                    info!("✅ {} SSE连接成功", quality_type);
                    retry_count = 0; // 重置重试计数
                }
                Err(e) => {
                    retry_count += 1;
                    error!("❌ {} SSE连接失败 (重试 {}/{}): {}", quality_type, retry_count, max_retries, e);
                    
                    if retry_count >= max_retries {
                        error!("🚫 {} SSE连接达到最大重试次数，停止重试", quality_type);
                        break;
                    }
                    
                    warn!("⏳ {} SSE将在{}秒后重试连接", quality_type, reconnect_interval);
                    sleep(Duration::from_secs(reconnect_interval)).await;
                }
            }
        }
        
        Ok(())
    }
    
    /// 连接并订阅SSE端点
    async fn connect_and_subscribe(
        endpoint: &str,
        quality_type: &str,
        sender: &Sender<UnifiedEvent>,
        builder: &FakeTradeBuilder,
    ) -> Result<()> {
        // 使用reqwest进行SSE连接
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()?;
        
        let response = client
            .get(endpoint)
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .send()
            .await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("HTTP状态码: {}", response.status()));
        }
        
        let mut stream = response.bytes_stream();
        use futures_util::StreamExt;
        
        while let Some(chunk) = stream.next().await {
            match chunk {
                Ok(bytes) => {
                    let data = String::from_utf8_lossy(&bytes);
                    if let Err(e) = Self::process_sse_data(&data, quality_type, sender, builder).await {
                        error!("处理SSE数据失败: {}", e);
                    }
                }
                Err(e) => {
                    error!("读取SSE数据流失败: {}", e);
                    break;
                }
            }
        }
        
        Ok(())
    }
    
    /// 处理SSE数据
    async fn process_sse_data(
        data: &str,
        quality_type: &str,
        sender: &Sender<UnifiedEvent>,
        builder: &FakeTradeBuilder,
    ) -> Result<()> {
        // 解析SSE数据
        for line in data.lines() {
            if line.starts_with("data: ") {
                let json_data = &line[6..]; // 去掉"data: "前缀
                
                match serde_json::from_str::<SseMintData>(json_data) {
                    Ok(mint_data) => {
                        debug!("📥 接收到{}信号: {:?}", quality_type, mint_data);
                        
                        // 构建假交易数据
                        let fake_trade = builder.build_fake_trade(&mint_data, quality_type);
                        
                        // 发送到统一发布器
                        let event = UnifiedEvent {
                            content: fake_trade,
                            timestamp: mint_data.timestamp,
                            publish_start: std::time::Instant::now(),
                            plugin_name: format!("sse_{}", quality_type),
                        };
                        
                        if let Err(e) = sender.send(event) {
                            error!("发送假交易到统一发布器失败: {}", e);
                        }
                    }
                    Err(e) => {
                        warn!("解析SSE数据失败: {}, 数据: {}", e, json_data);
                    }
                }
            }
        }
        
        Ok(())
    }
} 