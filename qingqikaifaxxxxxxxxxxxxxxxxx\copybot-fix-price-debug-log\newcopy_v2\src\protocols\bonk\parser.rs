use crate::shared::types::{HotPathTrade, TradeType};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use uuid::Uuid;
use std::sync::RwLock;
use once_cell::sync::Lazy;

// 全局存储最新解析的价格数据，初始值全部为0.0
pub static LAST_PRICE_BEFORE: Lazy<RwLock<f64>> = Lazy::new(|| RwLock::new(0.0));
pub static LAST_PRICE_AFTER: Lazy<RwLock<f64>> = Lazy::new(|| RwLock::new(0.0));
pub static LAST_SLIPPAGE: Lazy<RwLock<f64>> = Lazy::new(|| RwLock::new(0.0));
pub static LAST_ACTUAL_TRADE_PRICE: Lazy<RwLock<f64>> = Lazy::new(|| RwLock::new(0.0));

/// 获取最新解析的价格和滑点值
pub fn get_latest_price_data() -> (f64, f64, f64, f64) {
    let price_before = *LAST_PRICE_BEFORE.read().unwrap();
    let price_after = *LAST_PRICE_AFTER.read().unwrap();
    let slippage = *LAST_SLIPPAGE.read().unwrap();
    let actual_trade_price = *LAST_ACTUAL_TRADE_PRICE.read().unwrap();
    (price_before, price_after, slippage, actual_trade_price)
}

/// 从Redis接收到的bonk数据中提取关键信息并打印
pub fn parse_trades_from_redis_bytes(payload: &[u8], user_wallet_pubkey: &Pubkey) -> Vec<HotPathTrade> {
    // 将payload转为字符串
    let payload_str = String::from_utf8_lossy(payload);
    
    
    // 提取几个关键字段用于日志记录
    let mut signature = String::new();
    let mut direction = String::new();
    let mut signer = String::new();
    let mut mint = String::new();
    let mut pool_state = String::new();
    let mut base_vault = String::new();
    let mut quote_vault = String::new();
    let mut price = String::new();
    let mut price_before = String::new();
    let mut price_after = String::new();
    let mut slippage = String::new();
    let mut actual_trade_price = String::new();
    let mut amount_in = String::new();
    let mut amount_out = String::new();
    
    // 简单解析每一行数据提取关键信息
    for line in payload_str.lines() {
        let line = line.trim();
        
        if line.starts_with("signature\":") {
            signature = line.replace("signature\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("trade_direction\":") {
            direction = line.replace("trade_direction\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("signer\":") {
            signer = line.replace("signer\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("mint_address\":") {
            mint = line.replace("mint_address\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("pool_state\":") {
            pool_state = line.replace("pool_state\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("pool_base_vault\":") {
            base_vault = line.replace("pool_base_vault\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("pool_quote_vault\":") {
            quote_vault = line.replace("pool_quote_vault\": \"", "").replace("\",", "").replace("\"", "");
        } else if line.starts_with("price_before\":") {
            price_before = line.replace("price_before\": ", "").replace(",", "");
            if let Ok(val) = price_before.parse::<f64>() {
                let mut writer = LAST_PRICE_BEFORE.write().unwrap();
                *writer = val;
            }
        } else if line.starts_with("price_after\":") {
            price_after = line.replace("price_after\": ", "").replace(",", "");
            if let Ok(val) = price_after.parse::<f64>() {
                let mut writer = LAST_PRICE_AFTER.write().unwrap();
                *writer = val;
            }
        } else if line.starts_with("actual_trade_price\":") {
            actual_trade_price = line.replace("actual_trade_price\": ", "").replace(",", "");
            price = actual_trade_price.clone(); // 使用actual_trade_price作为正确的交易价格
            if let Ok(val) = actual_trade_price.parse::<f64>() {
                let mut writer = LAST_ACTUAL_TRADE_PRICE.write().unwrap();
                *writer = val;
            }
        } else if line.starts_with("slippage\":") {
            slippage = line.replace("slippage\": ", "").replace(",", "");
            if let Ok(val) = slippage.parse::<f64>() {
                let mut writer = LAST_SLIPPAGE.write().unwrap();
                *writer = val;
            }
        } else if line.starts_with("amount_in\":") {
            amount_in = line.replace("amount_in\": ", "").replace(",", "");
        } else if line.starts_with("amount_out\":") {
            amount_out = line.replace("amount_out\": ", "").replace(",", "");
        }
    }
    
    // 检查关键字段是否为空
    let mut missing_fields = Vec::new();
    if signer.is_empty() { missing_fields.push("钱包"); }
    if mint.is_empty() { missing_fields.push("代币"); }
    if pool_state.is_empty() { missing_fields.push("池状态"); }
    if base_vault.is_empty() { missing_fields.push("base_vault"); }
    if quote_vault.is_empty() { missing_fields.push("quote_vault"); }

    
    // 如果有足够的信息，创建HotPathTrade对象
    let mut result = Vec::new();
    if !signer.is_empty() && !mint.is_empty() && !signature.is_empty() {
        let trade_type = if direction.to_lowercase().contains("buy") {
            TradeType::Buy
        } else if direction.to_lowercase().contains("sell") {
            TradeType::Sell
        } else {
            TradeType::Unknown
        };
        
        // 解析所有必要的Pubkey
        if let Ok(mint_pubkey) = Pubkey::from_str(&mint) {
            // 解析池状态和金库地址
            let pool_pubkey = Pubkey::from_str(&pool_state).unwrap_or(mint_pubkey);
            let base_vault_pubkey = if !base_vault.is_empty() {
                Pubkey::from_str(&base_vault).unwrap_or(mint_pubkey)
            } else {
                mint_pubkey
            };
            
            let quote_vault_pubkey = if !quote_vault.is_empty() {
                Pubkey::from_str(&quote_vault).unwrap_or(mint_pubkey)
            } else {
                mint_pubkey
            };
            
            // 解析价格
            let price_f64 = price.parse::<f64>().unwrap_or(0.0);
            
            // 解析金额，根据交易方向处理
            let (sol_cost, token_amount) = match trade_type {
                TradeType::Buy => {
                    // 买入: amount_in是SOL(精度9)，amount_out是token(精度6)
                    let amount_in_lamports = amount_in.parse::<u64>().unwrap_or(0);
                    let sol_cost = amount_in_lamports as f64 / 1_000_000_000.0; // 转换lamports为SOL
                    let token_amount = amount_out.parse::<u64>().unwrap_or(0); 
                    
                    
                    (sol_cost, token_amount)
                },
                TradeType::Sell => {
                    // 卖出: amount_in是token(精度6)，amount_out是SOL(精度9)
                    let token_amount = amount_in.parse::<u64>().unwrap_or(0);
                    let amount_out_lamports = amount_out.parse::<u64>().unwrap_or(0);
                    let sol_cost = amount_out_lamports as f64 / 1_000_000_000.0; // 转换lamports为SOL
                    
                    
                    (sol_cost, token_amount)
                },
                TradeType::Unknown => {
                    // 未知类型，默认处理
                    let amount_in_value = amount_in.parse::<u64>().unwrap_or(0);
                    let amount_out_value = amount_out.parse::<u64>().unwrap_or(0);
                    
                    
                    (amount_in_value as f64 / 1_000_000_000.0, amount_out_value)
                }
            };
            
            // 创建HotPathTrade对象，使用正确的金库地址
            let trade = HotPathTrade {
                trade_id: Uuid::new_v4().to_string(),
                trade_type,
                signature,
                sol_cost,
                token_amount,
                signer,
                price: price_f64,
                protocol: Some("bonk".to_string()),
                mint_pubkey,
                creator_vault_pubkey: pool_pubkey,
                bonding_curve_pubkey: base_vault_pubkey,  // 使用pool_base_vault
                associated_bonding_curve: quote_vault_pubkey, // 使用pool_quote_vault
                user_ata: *user_wallet_pubkey,
                slippage_bps: 0,
            };
            
            result.push(trade);
        }
    }
    
    result
} 